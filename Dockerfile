FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Install required system packages
RUN apt-get update && apt-get install -y \
    nmap \
    whois \
    curl \
    wget \
    dnsutils \
    git \
    openssl \
    libssl-dev \
    python3-dev \
    build-essential \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Create directories
RUN mkdir -p osintdesk reports temp uploads

# Copy application code
COPY osintdesk/ ./osintdesk/
COPY docker-entrypoint.sh .

# Make entrypoint executable
RUN chmod +x docker-entrypoint.sh

# Create non-root user for security
RUN groupadd -r osintuser && useradd -r -g osintuser osintuser
RUN chown -R osintuser:osintuser /app

# Set permissions for directories
RUN chmod -R 755 /app/reports /app/temp /app/uploads /app/osintdesk

# Switch to non-root user
USER osintuser

# Expose the port the app runs on
EXPOSE 5001

# Set entrypoint
ENTRYPOINT ["/app/docker-entrypoint.sh"] 