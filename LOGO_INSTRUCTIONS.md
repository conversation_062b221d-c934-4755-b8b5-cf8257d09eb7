# 📋 Instrucciones para el Logo de Formula Hacking

## 🎯 Ubicación del Logo

Para completar el branding de Formula Hacking, necesitas colocar el archivo PNG original del logo en la siguiente ubicación:

```
static/images/formula-hacking-logo.png
```

## 📁 Estructura de Archivos

```
osintkaredesk/
├── static/
│   └── images/
│       └── formula-hacking-logo.png  ← COLOCAR AQUÍ EL LOGO ORIGINAL
├── templates/
├── web_portal.py
└── ...
```

## 🔧 Configuración Actual

El sistema ya está configurado para usar el logo PNG original en:

### 1. **Navbar (Barra de Navegación)**
- Ubicación: Esquina superior izquierda
- Tamaño: 40px de altura
- Ruta: `/static/images/formula-hacking-logo.png`

### 2. **Dashboard Principal**
- Ubicación: Header del dashboard
- Tamaño: 60px de altura
- Junto al título "Dashboard OSINT"

### 3. **Footer**
- Ubicación: Pie de página
- Tamaño: 20px de altura
- Junto al copyright

### 4. **Favicon**
- Icono del navegador
- Metadatos Open Graph y Twitter

## 📝 Especificaciones del Logo

### Formato Requerido:
- **Formato:** PNG (con transparencia)
- **Nombre:** `formula-hacking-logo.png`
- **Tamaño recomendado:** 200x80px o similar (proporción 2.5:1)
- **Fondo:** Transparente preferiblemente

### Características del Logo Original:
```
FORMULA
HACKING
```
- Texto "FORMULA" en la parte superior
- Texto "HACKING" en la parte inferior con espaciado
- Estilo tipográfico tecnológico/hacker
- Colores: Negro sobre fondo transparente o blanco

## 🚀 Pasos para Implementar

1. **Colocar el archivo PNG:**
   ```bash
   cp tu-logo-formula-hacking.png static/images/formula-hacking-logo.png
   ```

2. **Verificar la estructura:**
   ```bash
   ls -la static/images/
   # Debe mostrar: formula-hacking-logo.png
   ```

3. **Reiniciar el portal:**
   ```bash
   # Detener el portal actual (Ctrl+C)
   source venv/bin/activate
   python web_portal.py
   ```

4. **Verificar en el navegador:**
   - Ir a: http://localhost:8080
   - El logo debe aparecer en navbar, dashboard y footer

## 🎨 Branding Aplicado

### Elementos con Branding de Formula Hacking:

✅ **Navbar:** Logo + "OSINT Portal"
✅ **Dashboard:** Logo grande + título
✅ **Footer:** Logo pequeño + copyright
✅ **Favicon:** Logo como icono del navegador
✅ **Metadatos:** Open Graph y Twitter cards
✅ **Títulos:** "Formula Hacking - OSINT Portal"

### Páginas Actualizadas:
- ✅ Dashboard principal (`/`)
- ✅ Página de análisis (`/analyze`)
- ✅ Página de reportes (`/reports`)
- ✅ Visualización de reportes (`/visualize/*`)
- ✅ Todas las páginas (template base)

## 🔍 Verificación

Una vez colocado el logo, verifica que aparezca en:

1. **Barra de navegación** (esquina superior izquierda)
2. **Dashboard principal** (header grande)
3. **Footer** (pequeño junto al copyright)
4. **Favicon** (icono del navegador)
5. **Título de la página** (pestaña del navegador)

## 📞 Soporte

Si el logo no aparece después de colocarlo:

1. Verificar que el archivo esté en la ruta correcta
2. Verificar que el nombre sea exactamente: `formula-hacking-logo.png`
3. Reiniciar el portal web
4. Limpiar caché del navegador (Ctrl+F5)

---

**🎯 Una vez colocado el logo PNG original, el branding de Formula Hacking estará completamente implementado en todo el portal OSINT.**
