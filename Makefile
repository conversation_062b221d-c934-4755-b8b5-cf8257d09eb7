# OSINT Framework Makefile
# ======================

.PHONY: help install install-dev test test-unit test-integration test-network
.PHONY: coverage quality format lint typecheck security clean docs
.PHONY: run-basic run-advanced run-portal docker-build docker-run

# Default target
help:
	@echo "🔍 OSINT Security Analysis Framework"
	@echo "===================================="
	@echo ""
	@echo "Available commands:"
	@echo ""
	@echo "📦 Installation:"
	@echo "  install      - Install production dependencies"
	@echo "  install-dev  - Install development dependencies"
	@echo ""
	@echo "🧪 Testing:"
	@echo "  test         - Run all tests"
	@echo "  test-unit    - Run unit tests only"
	@echo "  test-integration - Run integration tests"
	@echo "  test-network - Run network-dependent tests"
	@echo "  coverage     - Generate coverage report"
	@echo ""
	@echo "🔧 Code Quality:"
	@echo "  quality      - Run all quality checks"
	@echo "  format       - Format code with black"
	@echo "  lint         - Run flake8 linting"
	@echo "  typecheck    - Run mypy type checking"
	@echo "  security     - Run security checks"
	@echo ""
	@echo "🚀 Running:"
	@echo "  run-basic    - Run basic OSINT analysis"
	@echo "  run-advanced - Run advanced OSINT analysis"
	@echo "  run-portal   - Start web portal"
	@echo ""
	@echo "🐳 Docker:"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run in Docker container"
	@echo ""
	@echo "📚 Documentation:"
	@echo "  docs         - Generate documentation"
	@echo "  clean        - Clean build artifacts"

# Installation targets
install:
	@echo "📦 Installing production dependencies..."
	pip install -r requirements.txt

install-dev:
	@echo "📦 Installing development dependencies..."
	pip install -e .[dev]
	pre-commit install

# Testing targets
test:
	@echo "🧪 Running all tests..."
	pytest -v

test-unit:
	@echo "🧪 Running unit tests..."
	pytest -v -m unit

test-integration:
	@echo "🧪 Running integration tests..."
	pytest -v -m integration

test-network:
	@echo "🧪 Running network tests..."
	pytest -v -m network

coverage:
	@echo "📊 Generating coverage report..."
	pytest --cov=. --cov-report=html --cov-report=term-missing
	@echo "📊 Coverage report generated in htmlcov/"

# Code quality targets
quality: format lint typecheck
	@echo "✅ All quality checks completed!"

format:
	@echo "🎨 Formatting code with black..."
	black .
	@echo "✅ Code formatting completed!"

lint:
	@echo "🔍 Running flake8 linting..."
	flake8 .
	@echo "✅ Linting completed!"

typecheck:
	@echo "🔍 Running mypy type checking..."
	mypy .
	@echo "✅ Type checking completed!"

security:
	@echo "🔒 Running security checks..."
	@command -v bandit >/dev/null 2>&1 || pip install bandit
	bandit -r . -x tests/
	@echo "✅ Security checks completed!"

# Running targets
run-basic:
	@echo "🚀 Starting basic OSINT analysis..."
	@read -p "Enter domain to analyze: " domain; \
	python osint_framework.py $$domain --authorized

run-advanced:
	@echo "🚀 Starting advanced OSINT analysis..."
	@read -p "Enter domain to analyze: " domain; \
	python advanced_osint_framework.py $$domain --authorized

run-portal:
	@echo "🌐 Starting web portal..."
	@echo "📊 Dashboard will be available at: http://localhost:8080"
	python web_portal.py

# Docker targets
docker-build:
	@echo "🐳 Building Docker image..."
	docker build -t osint-framework .
	@echo "✅ Docker image built successfully!"

docker-run:
	@echo "🐳 Running Docker container..."
	docker run -p 8080:8080 -it osint-framework

# Documentation targets
docs:
	@echo "📚 Generating documentation..."
	@command -v sphinx-build >/dev/null 2>&1 || pip install sphinx
	@mkdir -p docs
	@echo "📚 Documentation would be generated here"
	@echo "✅ Documentation generation completed!"

# Cleanup targets
clean:
	@echo "🧹 Cleaning build artifacts..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf htmlcov/
	rm -rf .coverage
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	@echo "✅ Cleanup completed!"

# Development workflow
dev-setup: install-dev
	@echo "🔧 Setting up development environment..."
	@echo "✅ Development environment ready!"
	@echo ""
	@echo "Next steps:"
	@echo "1. Run tests: make test"
	@echo "2. Check code quality: make quality"
	@echo "3. Start development: make run-portal"

# CI/CD simulation
ci: install-dev quality test coverage
	@echo "🎉 CI pipeline completed successfully!"

# Quick development check
check: format lint test-unit
	@echo "✅ Quick development check completed!"

# Release preparation
release-check: quality test coverage security
	@echo "🚀 Release checks completed!"
	@echo "📦 Ready for release!"

# Performance testing
perf-test:
	@echo "⚡ Running performance tests..."
	@command -v pytest-benchmark >/dev/null 2>&1 || pip install pytest-benchmark
	pytest tests/ -m "not network" --benchmark-only
	@echo "✅ Performance tests completed!"

# Example usage
example:
	@echo "📖 Running example analysis..."
	@echo "This will analyze example.com (safe for testing)"
	python osint_framework.py example.com --authorized

# Show project status
status:
	@echo "📊 Project Status"
	@echo "================"
	@echo "Python version: $$(python --version)"
	@echo "Dependencies: $$(pip list | wc -l) packages installed"
	@echo "Tests: $$(find tests -name '*.py' | wc -l) test files"
	@echo "Code files: $$(find . -name '*.py' -not -path './tests/*' -not -path './.venv/*' | wc -l) Python files"
	@echo "Last commit: $$(git log -1 --format='%h %s' 2>/dev/null || echo 'No git repository')"
