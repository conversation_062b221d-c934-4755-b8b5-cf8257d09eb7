# 🕵️ OSINT Security Analysis Framework

Un framework completo de análisis OSINT (Open Source Intelligence) con interfaz web moderna para análisis de seguridad de dominios.

## 🌟 Características Principales

### 🔍 Análisis OSINT Completo
- **Reconocimiento DNS**: Análisis completo de registros DNS (A, MX, TXT, NS)
- **Análisis SSL/TLS**: Verificación de certificados, cadenas de confianza y configuración
- **Enumeración de Subdominios**: Certificate Transparency, DNS bruteforce, APIs externas
- **Detección de Tecnologías**: Identificación de frameworks, servidores web, CMS
- **Análisis de Cabeceras de Seguridad**: Verificación de CSP, HSTS, X-Frame-Options, etc.

### 🛡️ Análisis Avanzado (Herramientas Profesionales)
- **Nuclei**: Escaneo de vulnerabilidades conocidas
- **Nmap**: Escaneo de puertos y servicios
- **Subfinder/Amass**: Enumeración avanzada de subdominios
- **HTTPX**: Verificación activa de subdominios
- **Wayback Machine**: Análisis histórico de URLs
- **Gobuster/FFUF**: Directory fuzzing
- **Integración con APIs**: Shodan, Censys

### 🌐 Portal Web Moderno
- **Dashboard Interactivo**: Visualización en tiempo real con WebSocket
- **Análisis en Tiempo Real**: Progreso live del análisis
- **Reportes Profesionales**: Generación automática de HTML y JSON
- **Gestión de Reportes**: Visualización, descarga y gestión completa
- **Interfaz Responsive**: Compatible con desktop y móvil
- **Graficos Interactivos**: Chart.js y Plotly.js

## 🚀 Instalación Rápida

### Prerequisitos
- Python 3.8+
- Git
- pip

### Instalación Automática
```bash
git clone https://github.com/tu-usuario/osintkaredesk.git
cd osintkaredesk
chmod +x install.sh
./install.sh
```

### Instalación de Herramientas Avanzadas (Opcional)
```bash
chmod +x install_advanced_tools.sh
./install_advanced_tools.sh
```

## 🎯 Uso Rápido

### Portal Web (Recomendado)
```bash
# Activar entorno virtual
source osint_env/bin/activate

# Iniciar portal web
python web_portal.py
```
Abrir navegador en: `http://localhost:8080`

### Línea de Comandos

#### Análisis Básico
```bash
python osint_framework.py ejemplo.com --authorized
```

#### Análisis Avanzado
```bash
python advanced_osint_framework.py ejemplo.com --authorized
```

## 📊 Ejemplos de Uso

### 1. Análisis desde Portal Web
1. Abrir `http://localhost:8080`
2. Ir a "Analizar"
3. Introducir dominio objetivo
4. Seleccionar tipo de análisis (Básico/Avanzado)
5. Revisar resultados en tiempo real

### 2. Análisis Programático
```python
from osint_framework import OSINTAnalyzer

# Crear analizador
analyzer = OSINTAnalyzer("ejemplo.com")

# Ejecutar análisis
results = analyzer.run_analysis()

# Generar reporte
report = analyzer.generate_report()
```

## 📁 Estructura del Proyecto

```
osintkaredesk/
├── 🐍 Core Python Files
│   ├── osint_framework.py          # Framework principal
│   ├── advanced_osint_framework.py # Análisis avanzado
│   ├── web_portal.py              # Portal web Flask
│   └── report_generator.py        # Generador de reportes
├── 🌐 Web Interface  
│   ├── templates/                 # Templates HTML
│   │   ├── base.html
│   │   ├── index.html
│   │   ├── analyze.html
│   │   ├── reports.html
│   │   └── visualize.html
│   └── static/                    # CSS/JS estáticos
├── ⚙️ Configuration & Setup
│   ├── requirements.txt           # Dependencias Python
│   ├── config.py                 # Configuración
│   ├── install.sh               # Instalador básico
│   └── install_advanced_tools.sh # Instalador avanzado
├── 📚 Documentation
│   ├── README.md                # Este archivo
│   ├── README_PORTAL.md         # Documentación del portal
│   ├── LICENSE                  # Licencia MIT
│   └── example_usage.py         # Ejemplos de código
└── 🛠️ Utilities
    └── start_portal.sh          # Script de inicio
```

## 🛡️ Consideraciones Éticas y Legales

⚠️ **IMPORTANTE**: Este framework está diseñado exclusivamente para:

- ✅ **Testing de penetración autorizado**
- ✅ **Auditorías de seguridad propias**
- ✅ **Investigación de seguridad legítima**
- ✅ **Análisis de dominios propios**

❌ **NO usar para**:
- Análisis no autorizados
- Actividades maliciosas
- Violación de términos de servicio
- Actividades ilegales

**El usuario es completamente responsable del uso ético y legal de esta herramienta.**

## 🔧 Configuración Avanzada

### APIs Externas (Opcional)
```python
# config.py
SHODAN_API_KEY = "tu_api_key_aquí"
CENSYS_API_ID = "tu_api_id_aquí"
CENSYS_API_SECRET = "tu_api_secret_aquí"
```

### Personalización del Portal
- Modificar `templates/` para personalizar la interfaz
- Ajustar `static/css/` para cambios de estilo
- Configurar `web_portal.py` para funcionalidades adicionales

## 📈 Características de los Reportes

### Formato JSON
- Datos estructurados para integración
- Metadatos completos
- Timestamps precisos
- Información de riesgo cuantificada

### Formato HTML
- Visualización profesional
- Gráficos interactivos
- Responsive design
- Descarga en PDF (navegador)

## 🔄 Actualizaciones y Mantenimiento

```bash
# Actualizar dependencias
pip install -r requirements.txt --upgrade

# Verificar herramientas avanzadas
./install_advanced_tools.sh --check
```

## 🤝 Contribuciones

Las contribuciones son bienvenidas! Por favor:

1. Fork el repositorio
2. Crea una rama para tu característica
3. Commit tus cambios
4. Push a la rama
5. Abre un Pull Request

## 📋 Requisitos del Sistema

### Mínimos
- Python 3.8+
- 2GB RAM
- 1GB espacio libre

### Recomendados
- Python 3.10+
- 4GB RAM
- 5GB espacio libre
- Conexión a Internet estable

## 🐛 Solución de Problemas

### Portal Web no inicia
```bash
# Verificar puerto
lsof -i :8080

# Reinstalar dependencias
pip install -r requirements.txt --force-reinstall
```

### Herramientas avanzadas no funcionan
```bash
# Verificar instalación
which nuclei nmap subfinder

# Reinstalar herramientas
./install_advanced_tools.sh --force
```

## 📞 Soporte

Para problemas técnicos:
1. Revisar logs en la terminal
2. Verificar configuración en `config.py`
3. Consultar documentación en `README_PORTAL.md`

## 📄 Licencia

Este proyecto está licenciado bajo la Licencia MIT - ver el archivo [LICENSE](LICENSE) para detalles.

## 🙏 Agradecimientos

- Comunidad OSINT por las metodologías
- Desarrolladores de herramientas open source utilizadas
- Contributors y testers

---

**⚡ ¡Comienza tu análisis OSINT en segundos!**

```bash
git clone https://github.com/tu-usuario/osintkaredesk.git && cd osintkaredesk && ./install.sh && python web_portal.py
``` 