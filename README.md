# 🕵️ OSINT Karedesk - Framework de Análisis Técnico Avanzado

Un framework profesional de OSINT (Open Source Intelligence) para análisis técnico avanzado de seguridad de dominios y sitios web.

## 🚀 Características Principales

### 🔒 **Análisis SSL/TLS Avanzado**
- **Información técnica completa**: Versión SSL/TLS, Cipher Suite, bits de encriptación
- **Evaluación de seguridad**: Clasificación automática (Seguro/Aceptable/Inseguro)
- **Detalles del certificado**: Algoritmo de clave pública, fingerprints SHA1/SHA256
- **Subject Alternative Names (SAN)**: Lista completa de dominios cubiertos
- **Extensiones avanzadas**: OCSP Stapling, Certificate Transparency
- **Cadena de certificados**: Análisis de longitud y validez

### 🔌 **Análisis de Puertos y Servicios**
- **Categorización inteligente**: Web, Mail, Database, Remote, etc.
- **Detección de servicios**: Identificación específica por puerto
- **Análisis de seguridad**: Evaluación de riesgo automática
- **Identificación de versiones**: Detección de software y versiones
- **Recomendaciones específicas**: Sugerencias por tipo de servicio
- **Escaneo concurrente**: Optimizado para velocidad

### 🌐 **Análisis DNS Técnico Completo**
- **IPv6 (AAAA)**: Soporte completo para direcciones IPv6
- **Registros CAA**: Certificate Authority Authorization
- **Seguridad de email**: SPF, DMARC, DKIM con análisis detallado
- **Reverse DNS**: Verificación de resolución inversa
- **Scoring de seguridad**: Puntuación automática de configuración DNS
- **Múltiples selectores DKIM**: Análisis de configuraciones complejas

### 📊 **Portal Web Técnico Avanzado**
- **Sección técnica dedicada**: Información detallada organizada
- **Visualización SSL/TLS**: Badges de seguridad y detalles técnicos
- **Análisis de puertos categorizado**: Servicios detectados y riesgos
- **Puntuación DNS visual**: Barras de progreso y métricas
- **Badges de estado**: Indicadores visuales para cada configuración

### 🛡️ **Análisis de Seguridad Integral**
- **Cabeceras de seguridad HTTP**: CSP, HSTS, X-Frame-Options, etc.
- **Evaluación de cookies**: Secure, HttpOnly, SameSite
- **Detección de tecnologías**: Frameworks, servidores, CMS
- **Recomendaciones priorizadas**: Alta, Media, Baja prioridad
- **Scoring de riesgo**: Evaluación automática del nivel de riesgo

## 📋 Requisitos

- Python 3.8+
- pip
- Conexión a Internet

## 🛠️ Instalación

### Instalación Automática (Recomendada)

```bash
# Clonar el repositorio
git clone https://github.com/stoja88/osintkaredesk.git
cd osintkaredesk

# Ejecutar script de instalación
chmod +x install.sh
./install.sh
```

### Instalación Manual

```bash
# Crear entorno virtual
python3 -m venv venv
source venv/bin/activate  # En Windows: venv\Scripts\activate

# Instalar dependencias
pip install -r requirements.txt
```

## 🎯 Uso

### Análisis Técnico Avanzado

```bash
# Activar entorno virtual
source venv/bin/activate

# Análisis técnico completo
python osint_framework.py google.com --authorized

# Análisis de dominio específico
python osint_framework.py tu-dominio.com --authorized
```

### Portal Web Técnico

```bash
# Iniciar portal web
./start_portal.sh

# O manualmente:
source venv/bin/activate
python web_portal.py
```

Acceder a: **http://localhost:8080**

## 🔬 Análisis Técnico Detallado

### 1. **SSL/TLS Profesional**
```
✓ Versión: TLSv1.3
✓ Cipher: TLS_AES_256_GCM_SHA384 (256 bits)
✓ Clave Pública: EC 256 bits
✓ SANs: 137 dominios
✓ OCSP Stapling: Habilitado
✓ Certificate Transparency: Verificado
```

### 2. **Puertos y Servicios**
```
✓ Categorización: Web, Database, Remote
✓ Servicios detectados: nginx/1.20.2, MySQL 8.0
✓ Análisis de seguridad: Riesgo Alto/Medio/Bajo
✓ Recomendaciones específicas por servicio
```

### 3. **DNS Avanzado**
```
✓ IPv6: 2001:db8::1
✓ SPF: v=spf1 include:_spf.google.com ~all
✓ DMARC: v=DMARC1; p=quarantine
✓ DKIM: 2 selectores configurados
✓ CAA: 0 issue "letsencrypt.org"
✓ Puntuación DNS: 92.5%
```

## 📊 Reportes Técnicos Avanzados

### Información Incluida
- **12+ campos SSL/TLS** técnicos detallados
- **Categorización de puertos** con detección de servicios
- **Scoring DNS** con métricas de seguridad
- **Análisis de email** (SPF, DMARC, DKIM)
- **Evaluación integral** con recomendaciones específicas

### Formatos de Salida
- **JSON estructurado**: Para procesamiento automatizado
- **HTML técnico**: Reportes visuales con métricas avanzadas
- **Portal web interactivo**: Visualización técnica completa

## 🎯 Casos de Uso Profesional

### 1. **Auditorías de Seguridad**
- Evaluación técnica completa de infraestructura
- Identificación de configuraciones inseguras
- Recomendaciones específicas de mejora

### 2. **Análisis de Compliance**
- Verificación de estándares de seguridad
- Evaluación de configuraciones DNS/SSL
- Documentación técnica detallada

### 3. **Monitoreo Continuo**
- Seguimiento de cambios en configuración
- Alertas de vencimiento de certificados
- Análisis de nuevos subdominios

## 📁 Estructura del Proyecto

```
osintkaredesk/
├── osint_framework.py      # Framework principal con análisis avanzado
├── web_portal.py          # Portal web técnico
├── report_generator.py    # Generador de reportes avanzados
├── config.py             # Configuración del sistema
├── templates/            # Templates HTML técnicos
│   ├── visualize.html    # Visualización técnica avanzada
│   └── ...
├── tests/               # Tests unitarios
├── requirements.txt     # Dependencias Python
├── Dockerfile          # Configuración Docker
└── README.md           # Documentación técnica
```

## 🎯 Comparación: Antes vs Después

### ❌ **Análisis Básico Anterior**
- Información SSL básica (solo versión y emisor)
- Escaneo de puertos simple
- DNS básico (A, MX, NS)
- Sin análisis de seguridad

### ✅ **Análisis Técnico Avanzado Actual**
- **SSL/TLS**: 12+ campos técnicos detallados
- **Puertos**: Categorización + detección + análisis de seguridad
- **DNS**: IPv6, CAA, SPF, DMARC, DKIM, scoring
- **Servicios**: Identificación de versiones y tecnologías
- **Seguridad**: Evaluación integral con recomendaciones

## ⚖️ Consideraciones Legales

**⚠️ IMPORTANTE**: Este framework está diseñado para:

- ✅ Análisis de seguridad autorizado
- ✅ Auditorías de infraestructura propia
- ✅ Investigación educativa y profesional
- ✅ Pentesting con permiso explícito

**❌ NO usar para**:
- Actividades ilegales o no autorizadas
- Violación de términos de servicio
- Cualquier actividad maliciosa

## 🐳 Docker

```bash
# Construcción y ejecución
docker build -t osint-karedesk .
docker run -p 8080:8080 osint-karedesk

# O usar docker-compose
docker-compose up -d
```

## 🧪 Testing

```bash
# Ejecutar tests
source venv/bin/activate
pytest tests/ -v

# Test de funcionalidades específicas
pytest tests/test_osint_framework.py::test_ssl_analysis -v
```

## 🤝 Contribución

1. Fork el proyecto
2. Crear rama feature (`git checkout -b feature/TechnicalImprovement`)
3. Commit cambios (`git commit -m 'Add advanced SSL analysis'`)
4. Push a la rama (`git push origin feature/TechnicalImprovement`)
5. Abrir Pull Request

## 📝 Licencia

Este proyecto está bajo la Licencia MIT. Ver `LICENSE` para más detalles.

## 🔗 Enlaces Útiles

- [Documentación del Portal Web](README_PORTAL.md)
- [Guía de Mejoras Técnicas](IMPROVEMENTS.md)
- [Issues y Bugs](https://github.com/stoja88/osintkaredesk/issues)

## 📞 Soporte Técnico

Para soporte técnico y preguntas:
- Crear un [Issue técnico](https://github.com/stoja88/osintkaredesk/issues)
- Revisar la documentación avanzada
- Consultar ejemplos de análisis en el código

---

**🔬 Desarrollado para análisis técnico profesional de ciberseguridad**