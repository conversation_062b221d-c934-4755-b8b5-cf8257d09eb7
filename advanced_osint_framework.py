#!/usr/bin/env python3
"""
Advanced OSINT Security Analysis Framework
==========================================

Framework avanzado con integración de herramientas profesionales:
- Nuclei para detección de vulnerabilidades
- Nmap para escaneo de puertos
- Subfinder para enumeración avanzada
- HTTPX para análisis HTTP
- Waybackurls para análisis histórico
- Amass para reconocimiento avanzado

IMPORTANTE: Solo para uso ético y autorizado.
"""

import sys
import os
import json
import subprocess
import requests
import socket
import ssl
import datetime
import argparse
import concurrent.futures
import time
import re
import threading
from urllib.parse import urlparse, urljoin
import dns.resolver
import whois
from bs4 import BeautifulSoup
import shodan
import censys.search
import warnings
warnings.filterwarnings('ignore')

class AdvancedOSINTAnalyzer:
    def __init__(self, domain, authorized=False, api_keys=None):
        self.domain = domain
        self.authorized = authorized
        self.api_keys = api_keys or {}
        self.results = {
            'domain': domain,
            'timestamp': datetime.datetime.now().isoformat(),
            'reconnaissance': {},
            'osint': {},
            'vulnerabilities': {},
            'advanced_scanning': {},
            'recommendations': [],
            'ethical_notice': "Este análisis fue realizado únicamente con información públicamente disponible."
        }
        
        # Verificar herramientas instaladas
        self.tools_available = self._check_tools()
        
        if not self.authorized:
            print("⚠️  AVISO ÉTICO: Asegúrese de tener autorización explícita antes de realizar cualquier análisis.")
    
    def _check_tools(self):
        """Verificar qué herramientas están disponibles"""
        tools = {
            'nuclei': self._check_command('nuclei'),
            'nmap': self._check_command('nmap'),
            'subfinder': self._check_command('subfinder'),
            'httpx': self._check_command('httpx'),
            'waybackurls': self._check_command('waybackurls'),
            'amass': self._check_command('amass'),
            'whatweb': self._check_command('whatweb'),
            'nikto': self._check_command('nikto'),
            'gobuster': self._check_command('gobuster'),
            'ffuf': self._check_command('ffuf')
        }
        
        print("\n🔧 Herramientas disponibles:")
        for tool, available in tools.items():
            status = "✅" if available else "❌"
            print(f"  {status} {tool}")
        
        return tools
    
    def _check_command(self, command):
        """Verificar si un comando está disponible"""
        try:
            subprocess.run([command, '--version'], capture_output=True, timeout=2)
            return True
        except:
            try:
                subprocess.run([command, '-h'], capture_output=True, timeout=2)
                return True
            except:
                return False
    
    def nuclei_scan(self):
        """Escaneo con Nuclei para detección de vulnerabilidades"""
        if not self.tools_available.get('nuclei'):
            print("⚠️  Nuclei no está instalado. Saltando escaneo de vulnerabilidades...")
            return {}
        
        print("\n🎯 Ejecutando escaneo con Nuclei...")
        vulnerabilities = []
        
        try:
            # Ejecutar Nuclei con templates básicos y timeout corto
            cmd = [
                'nuclei',
                '-u', f'https://{self.domain}',
                '-severity', 'critical,high',
                '-json',
                '-silent',
                '-timeout', '5',
                '-rate-limit', '5',
                '-no-update-templates'  # No actualizar templates para ser más rápido
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)  # Timeout de 60 segundos
            
            # Procesar resultados JSON línea por línea
            for line in result.stdout.strip().split('\n'):
                if line:
                    try:
                        vuln = json.loads(line)
                        vulnerabilities.append({
                            'template': vuln.get('template-id', ''),
                            'name': vuln.get('info', {}).get('name', ''),
                            'severity': vuln.get('info', {}).get('severity', ''),
                            'description': vuln.get('info', {}).get('description', ''),
                            'matched_at': vuln.get('matched-at', ''),
                            'type': vuln.get('type', '')
                        })
                    except:
                        pass
            
            print(f"✓ Encontradas {len(vulnerabilities)} vulnerabilidades potenciales")
            
        except subprocess.TimeoutExpired:
            print("⚠️  Nuclei timeout - continuando con análisis...")
        except Exception as e:
            print(f"⚠️  Error ejecutando Nuclei: {e}")
        
        self.results['advanced_scanning']['nuclei_vulnerabilities'] = vulnerabilities
        return vulnerabilities
    
    def nmap_scan(self):
        """Escaneo de puertos con Nmap"""
        if not self.tools_available.get('nmap'):
            print("⚠️  Nmap no está instalado. Saltando escaneo de puertos...")
            return {}
        
        print("\n🔍 Ejecutando escaneo de puertos con Nmap...")
        port_info = {}
        
        try:
            # Escaneo básico y rápido de puertos comunes
            cmd = [
                'nmap',
                '-sS',  # SYN scan
                '-F',   # Fast scan - solo puertos más comunes
                '--open',  # Solo puertos abiertos
                '-Pn',  # No ping
                '--max-retries', '1',
                '--host-timeout', '20s',
                '-T4',  # Timing agresivo
                self.domain
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)  # Timeout de 30 segundos
            
            # Parsear resultados básicos
            open_ports = []
            for line in result.stdout.split('\n'):
                if '/tcp' in line and 'open' in line:
                    parts = line.split()
                    if len(parts) >= 3:
                        port = parts[0].split('/')[0]
                        service = parts[2] if len(parts) > 2 else 'unknown'
                        open_ports.append({
                            'port': port,
                            'service': service,
                            'version': ''
                        })
            
            port_info['open_ports'] = open_ports
            print(f"✓ Encontrados {len(open_ports)} puertos abiertos")
            
        except subprocess.TimeoutExpired:
            print("⚠️  Nmap timeout - continuando con análisis...")
        except Exception as e:
            print(f"⚠️  Error ejecutando Nmap: {e}")
        
        self.results['advanced_scanning']['port_scan'] = port_info
        return port_info
    
    def advanced_subdomain_enumeration(self):
        """Enumeración avanzada de subdominios con múltiples herramientas"""
        print("\n🌐 Enumeración avanzada de subdominios...")
        all_subdomains = set()
        
        # Subfinder con timeout corto
        if self.tools_available.get('subfinder'):
            try:
                print("🔍 Ejecutando Subfinder...")
                cmd = ['subfinder', '-d', self.domain, '-silent', '-timeout', '10']
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=20)
                before = len(all_subdomains)
                for line in result.stdout.strip().split('\n'):
                    if line:
                        all_subdomains.add(line.strip())
                print(f"✓ Subfinder: {len(all_subdomains) - before} subdominios")
            except subprocess.TimeoutExpired:
                print("⚠️  Subfinder timeout - continuando...")
            except Exception as e:
                print(f"⚠️  Error con Subfinder: {e}")
        
        # Amass pasivo solamente para ser más rápido
        if self.tools_available.get('amass'):
            try:
                print("🔍 Ejecutando Amass (pasivo)...")
                cmd = ['amass', 'enum', '-passive', '-d', self.domain, '-timeout', '5']
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                before = len(all_subdomains)
                for line in result.stdout.strip().split('\n'):
                    if line and self.domain in line:
                        all_subdomains.add(line.strip())
                print(f"✓ Amass: {len(all_subdomains) - before} subdominios adicionales")
            except subprocess.TimeoutExpired:
                print("⚠️  Amass timeout - continuando...")
            except Exception as e:
                print(f"⚠️  Error con Amass: {e}")
        
        # Certificate Transparency (siempre disponible)
        try:
            print("🔍 Buscando en Certificate Transparency...")
            url = f"https://crt.sh/?q=%.{self.domain}&output=json"
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                before = len(all_subdomains)
                data = response.json()
                for entry in data:
                    name_value = entry.get('name_value', '')
                    for subdomain in name_value.split('\n'):
                        if subdomain.endswith(self.domain):
                            all_subdomains.add(subdomain)
                print(f"✓ Certificate Transparency: {len(all_subdomains) - before} subdominios adicionales")
        except Exception as e:
            print(f"⚠️  Error en Certificate Transparency: {e}")
        
        # Verificar subdominios activos con HTTPX (limitado)
        active_subdomains = []
        if self.tools_available.get('httpx') and all_subdomains:
            try:
                print("🔄 Verificando subdominios activos...")
                # Limitar a los primeros 20 subdominios para ser más rápido
                limited_subdomains = list(all_subdomains)[:20]
                
                with open('/tmp/subdomains.txt', 'w') as f:
                    f.write('\n'.join(limited_subdomains))
                
                cmd = ['httpx', '-l', '/tmp/subdomains.txt', '-silent', '-timeout', '3', '-threads', '10']
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                
                for line in result.stdout.strip().split('\n'):
                    if line:
                        active_subdomains.append(line.strip())
                
                os.remove('/tmp/subdomains.txt')
                print(f"✓ {len(active_subdomains)} subdominios activos verificados")
            except Exception as e:
                print(f"⚠️  Error verificando subdominios: {e}")
        
        self.results['osint']['all_subdomains'] = list(all_subdomains)
        self.results['osint']['active_subdomains'] = active_subdomains
        return all_subdomains
    
    def wayback_analysis(self):
        """Análisis histórico con Wayback Machine (simplificado)"""
        if not self.tools_available.get('waybackurls'):
            print("⚠️  Waybackurls no disponible - saltando análisis histórico")
            return {}
        
        print("📚 Analizando URLs históricas...")
        historical_data = {
            'interesting_files': []
        }
        
        try:
            cmd = ['waybackurls', self.domain]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=20)  # Timeout reducido
            
            count = 0
            for url in result.stdout.strip().split('\n'):
                if count >= 20:  # Máximo 20 URLs
                    break
                if url and any(ext in url.lower() for ext in ['.sql', '.bak', '.backup', '.env', '.git']):
                    historical_data['interesting_files'].append(url)
                    count += 1
            
            print(f"✓ Encontrados {len(historical_data['interesting_files'])} archivos históricos interesantes")
            
        except subprocess.TimeoutExpired:
            print("⚠️  Wayback timeout - continuando...")
        except Exception as e:
            print(f"⚠️  Error en análisis histórico: {e}")
        
        self.results['advanced_scanning']['wayback_analysis'] = historical_data
        return historical_data
    
    def technology_fingerprinting(self):
        """Fingerprinting avanzado de tecnologías"""
        print("\n🔬 Fingerprinting avanzado de tecnologías...")
        technologies = {}
        
        # WhatWeb
        if self.tools_available.get('whatweb'):
            try:
                cmd = ['whatweb', f'https://{self.domain}', '--color=never', '-a', '3']
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                
                # Parsear salida de WhatWeb
                tech_matches = re.findall(r'\[([^\]]+)\]', result.stdout)
                technologies['whatweb'] = tech_matches
                print(f"✓ WhatWeb detectó: {', '.join(tech_matches[:5])}")
            except:
                pass
        
        # Wappalyzer (mediante análisis manual)
        try:
            response = requests.get(f'https://{self.domain}', timeout=10)
            soup = BeautifulSoup(response.text, 'html.parser')
            headers = response.headers
            
            detected = []
            
            # Detectar CMS
            if 'wp-content' in response.text or 'wp-includes' in response.text:
                detected.append('WordPress')
            if 'Drupal' in headers.get('X-Generator', ''):
                detected.append('Drupal')
            if '/media/jui/' in response.text:
                detected.append('Joomla')
            
            # Detectar frameworks JavaScript
            if 'react' in response.text.lower():
                detected.append('React')
            if 'angular' in response.text.lower():
                detected.append('Angular')
            if 'vue' in response.text.lower():
                detected.append('Vue.js')
            
            # Detectar servidores
            if 'Server' in headers:
                detected.append(f"Server: {headers['Server']}")
            
            # Detectar CDNs
            if 'CF-Ray' in headers:
                detected.append('Cloudflare')
            if 'X-Amz-Cf-Id' in headers:
                detected.append('Amazon CloudFront')
            
            technologies['detected'] = detected
            
        except Exception as e:
            print(f"❌ Error en fingerprinting: {e}")
        
        self.results['advanced_scanning']['technology_fingerprinting'] = technologies
        return technologies
    
    def shodan_search(self):
        """Búsqueda en Shodan (requiere API key)"""
        if not self.api_keys.get('shodan'):
            print("⚠️  API key de Shodan no configurada. Saltando búsqueda...")
            return {}
        
        print("\n🔍 Buscando en Shodan...")
        shodan_data = {}
        
        try:
            api = shodan.Shodan(self.api_keys['shodan'])
            
            # Buscar por dominio
            results = api.search(self.domain)
            
            shodan_data['total_results'] = results['total']
            shodan_data['services'] = []
            
            for result in results['matches'][:10]:  # Limitar a 10 resultados
                service = {
                    'ip': result['ip_str'],
                    'port': result['port'],
                    'service': result.get('product', 'Unknown'),
                    'version': result.get('version', ''),
                    'os': result.get('os', ''),
                    'hostnames': result.get('hostnames', [])
                }
                shodan_data['services'].append(service)
            
            print(f"✓ Encontrados {shodan_data['total_results']} resultados en Shodan")
            
        except Exception as e:
            print(f"❌ Error en búsqueda Shodan: {e}")
        
        self.results['advanced_scanning']['shodan'] = shodan_data
        return shodan_data
    
    def directory_fuzzing(self):
        """Fuzzing básico de directorios (simplificado)"""
        if not (self.tools_available.get('gobuster') or self.tools_available.get('ffuf')):
            print("⚠️  Gobuster/FFUF no disponibles - saltando fuzzing")
            return {}
        
        print("📁 Fuzzing básico de directorios...")
        directories = []
        
        # Directorios comunes a verificar manualmente
        common_dirs = ['/admin', '/login', '/api', '/test', '/dev', '/backup', '/config']
        
        try:
            for dir_path in common_dirs:
                try:
                    response = requests.get(f'https://{self.domain}{dir_path}', timeout=3, verify=False, allow_redirects=False)
                    if response.status_code in [200, 301, 302, 403]:
                        directories.append({
                            'path': dir_path,
                            'status': response.status_code,
                            'size': len(response.content)
                        })
                except:
                    pass
            
            print(f"✓ Encontrados {len(directories)} directorios de interés")
            
        except Exception as e:
            print(f"⚠️  Error en fuzzing: {e}")
        
        self.results['advanced_scanning']['directories'] = directories
        return directories
    
    def generate_advanced_recommendations(self):
        """Generar recomendaciones avanzadas basadas en todos los hallazgos"""
        recommendations = []
        
        # Recomendaciones basadas en Nuclei
        nuclei_vulns = self.results.get('advanced_scanning', {}).get('nuclei_vulnerabilities', [])
        critical_vulns = [v for v in nuclei_vulns if v.get('severity') == 'critical']
        high_vulns = [v for v in nuclei_vulns if v.get('severity') == 'high']
        
        if critical_vulns:
            for vuln in critical_vulns:
                recommendations.append({
                    'title': f"Vulnerabilidad Crítica: {vuln.get('name', 'Sin nombre')}",
                    'description': vuln.get('description', 'Vulnerabilidad crítica detectada'),
                    'priority': 'Alta',
                    'category': 'Vulnerabilidad',
                    'impact': 'Compromiso total del sistema'
                })
        
        if high_vulns:
            for vuln in high_vulns[:3]:  # Limitar a 3
                recommendations.append({
                    'title': f"Vulnerabilidad Alta: {vuln.get('name', 'Sin nombre')}",
                    'description': vuln.get('description', 'Vulnerabilidad de alta severidad'),
                    'priority': 'Alta',
                    'category': 'Vulnerabilidad',
                    'impact': 'Posible compromiso de datos'
                })
        
        # Recomendaciones basadas en puertos abiertos
        open_ports = self.results.get('advanced_scanning', {}).get('port_scan', {}).get('open_ports', [])
        risky_ports = ['21', '23', '445', '3389']  # FTP, Telnet, SMB, RDP
        
        for port_info in open_ports:
            if port_info['port'] in risky_ports:
                recommendations.append({
                    'title': f"Puerto riesgoso abierto: {port_info['port']} ({port_info['service']})",
                    'description': f"El puerto {port_info['port']} está abierto y puede representar un riesgo de seguridad",
                    'priority': 'Media',
                    'category': 'Configuración',
                    'impact': 'Posible vector de ataque'
                })
        
        # Recomendaciones basadas en archivos históricos
        interesting_files = self.results.get('advanced_scanning', {}).get('wayback_analysis', {}).get('interesting_files', [])
        if interesting_files:
            recommendations.append({
                'title': 'Archivos sensibles en histórico',
                'description': f'Se encontraron {len(interesting_files)} archivos potencialmente sensibles en el histórico web',
                'priority': 'Media',
                'category': 'Exposición de Información',
                'impact': 'Posible fuga de información'
            })
        
        # Agregar recomendaciones básicas del framework original
        security_headers = self.results.get('vulnerabilities', {}).get('security_headers', {})
        
        if security_headers.get('Content-Security-Policy') == 'Ausente':
            recommendations.append({
                'title': 'Implementar Content-Security-Policy',
                'description': 'CSP ayuda a prevenir ataques XSS y de inyección de código',
                'priority': 'Alta',
                'category': 'Cabeceras de Seguridad',
                'impact': 'Prevención de XSS'
            })
        
        # Recomendación de monitoreo continuo
        recommendations.append({
            'title': 'Implementar monitoreo continuo',
            'description': 'Establecer monitoreo 24/7 con herramientas como Nuclei, Shodan Monitor',
            'priority': 'Alta',
            'category': 'Monitoreo',
            'impact': 'Detección temprana de vulnerabilidades'
        })
        
        self.results['recommendations'] = recommendations
        return recommendations
    
    def run_advanced_analysis(self):
        """Ejecutar análisis avanzado completo con timeouts optimizados"""
        print(f"\n🚀 Iniciando análisis OSINT AVANZADO para: {self.domain}")
        print("=" * 60)
        
        # Fase 1: Análisis básico (max 30 segundos)
        print("\n🔍 FASE 1: Análisis básico...")
        try:
            self.basic_domain_info()
            self.ssl_analysis()
            self.security_headers_analysis()
            print("✅ Análisis básico completado")
        except Exception as e:
            print(f"⚠️  Error en análisis básico: {e}")
        
        # Fase 2: Subdominios avanzados (max 60 segundos)
        print("\n🌐 FASE 2: Enumeración avanzada de subdominios...")
        try:
            self.advanced_subdomain_enumeration()
            print("✅ Enumeración de subdominios completada")
        except Exception as e:
            print(f"⚠️  Error en enumeración de subdominios: {e}")
        
        # Fase 3: Escaneo de vulnerabilidades (max 90 segundos)
        print("\n🎯 FASE 3: Detección de vulnerabilidades...")
        try:
            self.nuclei_scan()
            print("✅ Escaneo de vulnerabilidades completado")
        except Exception as e:
            print(f"⚠️  Error en escaneo de vulnerabilidades: {e}")
        
        # Fase 4: Escaneo de puertos (max 45 segundos)
        print("\n🔍 FASE 4: Escaneo de puertos...")
        try:
            self.nmap_scan()
            print("✅ Escaneo de puertos completado")
        except Exception as e:
            print(f"⚠️  Error en escaneo de puertos: {e}")
        
        # Fase 5: Análisis adicionales (en paralelo)
        print("\n🕵️ FASE 5: Análisis adicionales...")
        additional_analyses = [
            ("Wayback Machine", self.wayback_analysis),
            ("Fingerprinting", self.technology_fingerprinting),
            ("Directory Fuzzing", self.directory_fuzzing),
            ("Shodan Search", self.shodan_search)
        ]
        
        for name, func in additional_analyses:
            try:
                print(f"🔄 {name}...")
                func()
                print(f"✅ {name} completado")
            except Exception as e:
                print(f"⚠️  Error en {name}: {e}")
        
        # Fase 6: Generar recomendaciones
        print("\n📝 FASE 6: Generando recomendaciones...")
        try:
            self.generate_advanced_recommendations()
            print("✅ Recomendaciones generadas")
        except Exception as e:
            print(f"⚠️  Error generando recomendaciones: {e}")
        
        # Fase 7: Generar informe
        print("\n📊 FASE 7: Generando informe...")
        try:
            report = self.generate_report()
            print("✅ Informe generado")
        except Exception as e:
            print(f"⚠️  Error generando informe: {e}")
            report = self.results
        
        print("\n" + "=" * 60)
        print("🎉 Análisis avanzado completado")
        print(f"📊 Total de hallazgos: {len(self.results.get('recommendations', []))}")
        print(f"📄 Reporte guardado en: {getattr(self, 'report_filename', 'N/A')}")
        
        return report
    
    def basic_domain_info(self):
        """Información básica del dominio (mejorada)"""
        print("\n🔍 Recolectando información del dominio...")
        
        # DNS
        try:
            ip = socket.gethostbyname(self.domain)
            self.results['reconnaissance']['ip_address'] = ip
            
            # Información DNS adicional
            dns_info = {
                'ip_address': ip,
                'a_records': [],
                'mx_records': [],
                'txt_records': [],
                'ns_records': []
            }
            
            resolver = dns.resolver.Resolver()
            
            # Registros A
            try:
                for rdata in resolver.resolve(self.domain, 'A'):
                    dns_info['a_records'].append(str(rdata))
            except:
                pass
            
            # Registros MX
            try:
                for rdata in resolver.resolve(self.domain, 'MX'):
                    dns_info['mx_records'].append(f"{rdata.preference} {rdata.exchange}")
            except:
                pass
            
            # Registros TXT
            try:
                for rdata in resolver.resolve(self.domain, 'TXT'):
                    dns_info['txt_records'].append(str(rdata))
            except:
                pass
            
            self.results['reconnaissance']['dns_info'] = dns_info
            print(f"✓ IP: {ip}")
            
        except Exception as e:
            print(f"❌ Error obteniendo información DNS: {e}")
        
        # WHOIS
        try:
            w = whois.whois(self.domain)
            whois_info = {
                'registrar': w.registrar,
                'creation_date': str(w.creation_date) if w.creation_date else None,
                'expiration_date': str(w.expiration_date) if w.expiration_date else None,
                'name_servers': w.name_servers if w.name_servers else []
            }
            self.results['reconnaissance']['whois'] = whois_info
            print(f"✓ Registrar: {whois_info['registrar']}")
        except:
            pass
    
    def ssl_analysis(self):
        """Análisis SSL mejorado"""
        print("\n🔒 Analizando SSL/TLS...")
        
        try:
            context = ssl.create_default_context()
            with socket.create_connection((self.domain, 443), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=self.domain) as ssock:
                    cert = ssock.getpeercert()
                    
                    ssl_info = {
                        'ssl_version': ssock.version(),
                        'cipher_suite': str(ssock.cipher()[0]) if ssock.cipher() else None,
                        'issuer': dict(x[0] for x in cert['issuer']),
                        'subject': dict(x[0] for x in cert['subject']),
                        'expiry_date': cert['notAfter'],
                        'san': []
                    }
                    
                    # Subject Alternative Names
                    for ext in cert.get('subjectAltName', []):
                        if ext[0] == 'DNS':
                            ssl_info['san'].append(ext[1])
                    
                    self.results['reconnaissance']['ssl_info'] = ssl_info
                    print(f"✓ SSL Version: {ssl_info['ssl_version']}")
                    print(f"✓ Certificado válido hasta: {ssl_info['expiry_date']}")
                    
        except Exception as e:
            print(f"❌ Error en análisis SSL: {e}")
    
    def security_headers_analysis(self):
        """Análisis de cabeceras de seguridad"""
        print("\n🛡️ Analizando cabeceras de seguridad...")
        
        security_headers = {}
        try:
            response = requests.get(f"https://{self.domain}", timeout=10, verify=False)
            headers = response.headers
            
            security_header_list = [
                'Content-Security-Policy',
                'X-XSS-Protection',
                'X-Frame-Options',
                'Strict-Transport-Security',
                'Referrer-Policy',
                'Permissions-Policy',
                'X-Content-Type-Options',
                'Feature-Policy',
                'Expect-CT',
                'X-Permitted-Cross-Domain-Policies'
            ]
            
            for header in security_header_list:
                if header in headers:
                    security_headers[header] = headers[header]
                    print(f"✓ {header}: Presente")
                else:
                    security_headers[header] = "Ausente"
                    print(f"⚠️ {header}: Ausente")
        
        except Exception as e:
            print(f"❌ Error analizando cabeceras: {e}")
        
        self.results['vulnerabilities']['security_headers'] = security_headers
        return security_headers
    
    def generate_report(self):
        """Generar informe avanzado"""
        print("\n📊 Generando informe avanzado...")
        
        # Calcular métricas
        total_vulns = len(self.results.get('advanced_scanning', {}).get('nuclei_vulnerabilities', []))
        total_subdomains = len(self.results.get('osint', {}).get('all_subdomains', []))
        active_subdomains = len(self.results.get('osint', {}).get('active_subdomains', []))
        open_ports = len(self.results.get('advanced_scanning', {}).get('port_scan', {}).get('open_ports', []))
        
        report = {
            'executive_summary': {
                'domain': self.domain,
                'analysis_date': self.results['timestamp'],
                'total_findings': len(self.results.get('recommendations', [])),
                'risk_level': self._calculate_advanced_risk_level(),
                'vulnerabilities_found': total_vulns,
                'subdomains_found': total_subdomains,
                'active_subdomains': active_subdomains,
                'open_ports': open_ports
            },
            'detailed_findings': self.results,
            'recommendations': self.results.get('recommendations', [])
        }
        
        # Guardar informe
        filename = f"osint_report_{self.domain}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.report_filename = filename
        print(f"✓ Informe guardado en: {filename}")
        
        # Generar informe HTML también
        try:
            from report_generator import ReportGenerator
            generator = ReportGenerator(filename)
            html_filename = generator.generate_html_report()
            print(f"✅ Informe HTML generado: {html_filename}")
        except Exception as e:
            print(f"⚠️  Error generando HTML: {e}")
        
        return report
    
    def _calculate_advanced_risk_level(self):
        """Calcular nivel de riesgo avanzado"""
        score = 0
        
        # Vulnerabilidades Nuclei
        nuclei_vulns = self.results.get('advanced_scanning', {}).get('nuclei_vulnerabilities', [])
        critical_vulns = sum(1 for v in nuclei_vulns if v.get('severity') == 'critical')
        high_vulns = sum(1 for v in nuclei_vulns if v.get('severity') == 'high')
        
        score += critical_vulns * 10
        score += high_vulns * 5
        
        # Cabeceras de seguridad
        security_headers = self.results.get('vulnerabilities', {}).get('security_headers', {})
        missing_headers = sum(1 for v in security_headers.values() if v == 'Ausente')
        score += missing_headers * 2
        
        # Puertos riesgosos
        open_ports = self.results.get('advanced_scanning', {}).get('port_scan', {}).get('open_ports', [])
        risky_ports = sum(1 for p in open_ports if p['port'] in ['21', '23', '445', '3389'])
        score += risky_ports * 3
        
        # Determinar nivel
        if score >= 30:
            return 'Alto'
        elif score >= 15:
            return 'Medio'
        else:
            return 'Bajo'

def main():
    parser = argparse.ArgumentParser(description='Advanced OSINT Security Analysis Framework')
    parser.add_argument('domain', help='Dominio a analizar')
    parser.add_argument('--authorized', action='store_true', help='Confirmar autorización')
    parser.add_argument('--shodan-key', help='API key de Shodan')
    parser.add_argument('--censys-id', help='Censys API ID')
    parser.add_argument('--censys-secret', help='Censys API Secret')
    
    args = parser.parse_args()
    
    # Configurar API keys
    api_keys = {}
    if args.shodan_key:
        api_keys['shodan'] = args.shodan_key
    if args.censys_id and args.censys_secret:
        api_keys['censys'] = {'id': args.censys_id, 'secret': args.censys_secret}
    
    # Limpiar dominio
    domain = args.domain.replace('https://', '').replace('http://', '').replace('www.', '')
    
    analyzer = AdvancedOSINTAnalyzer(domain, args.authorized, api_keys)
    analyzer.run_advanced_analysis()

if __name__ == "__main__":
    main() 