#! osintdesk-deployment.yaml
# Generated code, do not edit
apiVersion: apps/v1
kind: Deployment
metadata:
    name: osintdesk
    namespace: osintdesk-docker
    labels:
        com.docker.compose.project: osintdesk-docker
        com.docker.compose.service: osintdesk
spec:
    replicas: 1
    selector:
        matchLabels:
            com.docker.compose.project: osintdesk-docker
            com.docker.compose.service: osintdesk
    strategy:
        type: Recreate
    template:
        metadata:
            labels:
                com.docker.compose.project: osintdesk-docker
                com.docker.compose.service: osintdesk
                com.docker.compose.network.osintdesk-network: "true"
        spec:
            restartPolicy: unless-stopped
            containers:
                - name: osintdesk
                  image: osintdesk-docker-osintdesk
                  imagePullPolicy: IfNotPresent
                  env:
                    - name: CENSYS_API_ID
                      value: ""
                    - name: CENSYS_API_SECRET
                      value: ""
                    - name: FLASK_APP
                      value: "osintdesk/app.py"
                    - name: FLASK_ENV
                      value: "production"
                    - name: HUNTER_API_KEY
                      value: ""
                    - name: MAXMIND_LICENSE_KEY
                      value: ""
                    - name: SECRET_KEY
                      value: "changeme_in_production"
                    - name: SECURITY_TRAILS_API_KEY
                      value: ""
                    - name: SHODAN_API_KEY
                      value: ""
                    - name: VIRUSTOTAL_API_KEY
                      value: ""
                  ports:
                    - name: osintdesk-5001
                      containerPort: 5001
                  volumeMounts:
                    - name: app-osintdesk
                      mountPath: /app/osintdesk
                    - name: app-reports
                      mountPath: /app/reports
                    - name: app-temp
                      mountPath: /app/temp
                    - name: app-uploads
                      mountPath: /app/uploads
            volumes:
                - name: app-osintdesk
                  hostPath:
                    path: /Users/<USER>/carpeta sin título 8/osintdesk-docker/osintdesk
                - name: app-reports
                  hostPath:
                    path: /Users/<USER>/carpeta sin título 8/osintdesk-docker/reports
                - name: app-temp
                  hostPath:
                    path: /Users/<USER>/carpeta sin título 8/osintdesk-docker/temp
                - name: app-uploads
                  hostPath:
                    path: /Users/<USER>/carpeta sin título 8/osintdesk-docker/uploads
