#! osintdesk-network-network-policy.yaml
# Generated code, do not edit
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
    name: osintdesk-network-network-policy
    namespace: osintdesk-docker
spec:
    podSelector:
        matchLabels:
            com.docker.compose.network.osintdesk-network: "true"
    policyTypes:
        - Ingress
        - Egress
    ingress:
        - from:
            - podSelector:
                matchLabels:
                    com.docker.compose.network.osintdesk-network: "true"
    egress:
        - to:
            - podSelector:
                matchLabels:
                    com.docker.compose.network.osintdesk-network: "true"
