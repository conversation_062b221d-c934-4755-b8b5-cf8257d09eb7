#!/usr/bin/env python3
"""
Script para crear un logo temporal de Formula Hacking
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_formula_hacking_logo():
    """Crear logo temporal de Formula Hacking"""
    
    # Dimensiones del logo
    width = 200
    height = 80
    
    # Crear imagen con fondo transparente
    img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Colores
    bg_color = (0, 0, 0, 255)  # Negro
    text_color = (0, 255, 0, 255)  # Verde brillante
    text_color2 = (255, 255, 255, 255)  # Blanco
    
    # Fondo negro con bordes redondeados
    draw.rounded_rectangle([2, 2, width-2, height-2], radius=8, fill=bg_color)
    
    # Borde verde
    draw.rounded_rectangle([2, 2, width-2, height-2], radius=8, outline=text_color, width=2)
    
    try:
        # Intentar usar una fuente monospace
        font_large = ImageFont.truetype("/System/Library/Fonts/Courier.ttc", 24)
        font_small = ImageFont.truetype("/System/Library/Fonts/Courier.ttc", 12)
    except:
        try:
            # Fallback para otros sistemas
            font_large = ImageFont.truetype("arial.ttf", 24)
            font_small = ImageFont.truetype("arial.ttf", 12)
        except:
            # Usar fuente por defecto
            font_large = ImageFont.load_default()
            font_small = ImageFont.load_default()
    
    # Texto "FORMULA"
    text1 = "FORMULA"
    bbox1 = draw.textbbox((0, 0), text1, font=font_large)
    text1_width = bbox1[2] - bbox1[0]
    text1_x = (width - text1_width) // 2
    text1_y = 15
    
    draw.text((text1_x, text1_y), text1, fill=text_color, font=font_large)
    
    # Texto "HACKING"
    text2 = "H A C K I N G"
    bbox2 = draw.textbbox((0, 0), text2, font=font_small)
    text2_width = bbox2[2] - bbox2[0]
    text2_x = (width - text2_width) // 2
    text2_y = 50
    
    draw.text((text2_x, text2_y), text2, fill=text_color2, font=font_small)
    
    # Elementos decorativos
    # Líneas en las esquinas
    draw.line([10, 10, 25, 10], fill=text_color, width=2)
    draw.line([10, 10, 10, 25], fill=text_color, width=2)
    
    draw.line([width-25, 10, width-10, 10], fill=text_color, width=2)
    draw.line([width-10, 10, width-10, 25], fill=text_color, width=2)
    
    draw.line([10, height-25, 10, height-10], fill=text_color, width=2)
    draw.line([10, height-10, 25, height-10], fill=text_color, width=2)
    
    draw.line([width-10, height-25, width-10, height-10], fill=text_color, width=2)
    draw.line([width-25, height-10, width-10, height-10], fill=text_color, width=2)
    
    # Puntos decorativos
    for x, y in [(30, 30), (170, 30), (30, 50), (170, 50)]:
        draw.ellipse([x-2, y-2, x+2, y+2], fill=text_color)
    
    return img

def main():
    """Función principal"""
    print("🎨 Creando logo temporal de Formula Hacking...")
    
    # Crear directorio si no existe
    os.makedirs('static/images', exist_ok=True)
    
    # Crear logo
    logo = create_formula_hacking_logo()
    
    # Guardar logo
    logo_path = 'static/images/formula-hacking-logo.png'
    logo.save(logo_path, 'PNG')
    
    print(f"✅ Logo temporal creado: {logo_path}")
    print("📝 Este es un logo temporal para demostración")
    print("🔄 Reemplaza con el logo PNG original de Formula Hacking")
    print("📍 Ubicación: static/images/formula-hacking-logo.png")

if __name__ == "__main__":
    main()
