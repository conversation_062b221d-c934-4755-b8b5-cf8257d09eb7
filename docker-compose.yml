version: '3'

services:
  osintdesk:
    build: .
    container_name: osintdesk
    restart: unless-stopped
    ports:
      - "5001:5001"
    volumes:
      - ./osintdesk:/app/osintdesk
      - ./reports:/app/reports
      - ./temp:/app/temp
      - ./uploads:/app/uploads
    environment:
      - FLASK_APP=osintdesk/app.py
      - FLASK_ENV=production
      - SECRET_KEY=changeme_in_production
      - SHODAN_API_KEY=${SHODAN_API_KEY:-}
      - CENSYS_API_ID=${CENSYS_API_ID:-}
      - CENSYS_API_SECRET=${CENSYS_API_SECRET:-}
      - VIRUSTOTAL_API_KEY=${VIRUSTOTAL_API_KEY:-}
      - HUNTER_API_KEY=${HUNTER_API_KEY:-}
      - SECURITY_TRAILS_API_KEY=${SECURITY_TRAILS_API_KEY:-}
      - MAXMIND_LICENSE_KEY=${MAXMIND_LICENSE_KEY:-}
    networks:
      - osintdesk-network

networks:
  osintdesk-network:
    driver: bridge 