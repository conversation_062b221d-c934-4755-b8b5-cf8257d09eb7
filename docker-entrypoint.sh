#!/bin/bash
set -e

echo "======================================================================================"
echo "                   OSINTdesk - Starting Container Initialization                      "
echo "======================================================================================"

# Create necessary directories if they don't exist
mkdir -p /app/reports
mkdir -p /app/temp
mkdir -p /app/uploads

# Set permissions
chmod -R 755 /app/reports
chmod -R 755 /app/temp
chmod -R 755 /app/uploads

# Check for API keys and warn if missing
if [ -z "$SHODAN_API_KEY" ]; then
  echo "WARNING: SHODAN_API_KEY is not set. Some Shodan functionality will be limited."
fi

if [ -z "$CENSYS_API_ID" ] || [ -z "$CENSYS_API_SECRET" ]; then
  echo "WARNING: Censys API credentials are not set. Censys functionality will be limited."
fi

if [ -z "$VIRUSTOTAL_API_KEY" ]; then
  echo "WARNING: VIRUSTOTAL_API_KEY is not set. VirusTotal functionality will be limited."
fi

if [ -z "$HUNTER_API_KEY" ]; then
  echo "WARNING: HUNTER_API_KEY is not set. Email harvesting functionality will be limited."
fi

if [ -z "$SECURITY_TRAILS_API_KEY" ]; then
  echo "WARNING: SECURITY_TRAILS_API_KEY is not set. SecurityTrails functionality will be limited."
fi

# Update GeoIP database if needed
if [ ! -f "/app/temp/GeoLite2-City.mmdb" ]; then
  echo "GeoIP database not found, attempting to download..."
  if [ ! -z "$MAXMIND_LICENSE_KEY" ]; then
    echo "Downloading GeoIP database using provided MaxMind license key..."
    curl -s "https://download.maxmind.com/app/geoip_download?edition_id=GeoLite2-City&license_key=$MAXMIND_LICENSE_KEY&suffix=tar.gz" | tar -xz -C /tmp
    mv /tmp/GeoLite2-City_*/GeoLite2-City.mmdb /app/temp/ 2>/dev/null || echo "Failed to extract GeoIP database"
    rm -rf /tmp/GeoLite2-City_* 2>/dev/null
    if [ -f "/app/temp/GeoLite2-City.mmdb" ]; then
      echo "GeoIP database downloaded successfully."
    else
      echo "ERROR: Failed to download GeoIP database. Check your MAXMIND_LICENSE_KEY."
    fi
  else
    echo "MAXMIND_LICENSE_KEY not set, skipping GeoIP database download."
  fi
fi

# Verify critical tool installations
echo "Verifying tool installations..."
TOOLS_OK=true

# Check essential command-line tools
for tool in nmap whois host dig nslookup nikto sslscan hydra medusa sqlmap; do
  if ! command -v $tool &> /dev/null; then
    echo "WARNING: $tool command not found in PATH. Some functionality may be affected."
    TOOLS_OK=false
  else
    echo "✅ $tool is installed."
  fi
done

# Check Python modules
for module in shodan censys sublist3r dnsrecon wafw00f requests; do
  if ! python -c "import $module" &> /dev/null; then
    echo "WARNING: Python module '$module' not found. Some functionality may be affected."
    TOOLS_OK=false
  else
    echo "✅ Python module '$module' is installed."
  fi
done

# CMSeeK check
if [ ! -d "/opt/tools/CMSeeK" ]; then
  echo "WARNING: CMSeeK not found in /opt/tools/CMSeeK. CMS detection will be affected."
  TOOLS_OK=false
else
  echo "✅ CMSeeK is installed."
fi

if [ "$TOOLS_OK" = false ]; then
  echo "NOTICE: Some tools appear to be missing. OSINTdesk will run with limited functionality."
else
  echo "All required tools installed successfully."
fi

echo "======================================================================================"
echo "                   OSINTdesk - Initialization Complete                               "
echo "======================================================================================"

echo "======================================================================================"
echo "OSINTdesk is starting at http://localhost:5001"
echo "======================================================================================"

# Run the application with Gunicorn
# Use Python directly instead of Gunicorn for easier debugging
cd /app
exec python -m osintdesk.app 