FROM kalilinux/kali-rolling

# Evitar interacciones durante la instalación
ENV DEBIAN_FRONTEND=noninteractive

# Actualizar e instalar herramientas básicas
RUN apt-get update && apt-get install -y \
    python3 python3-pip python3-dev \
    whois dnsutils bind9-utils \
    nmap sslscan wafw00f \
    dnsrecon nikto \
    git wget curl \
    # Herramientas adicionales mencionadas en app.py
    hydra medusa \
    responder bettercap \
    testssl.sh \
    # Dependencias para herramientas de visualización
    libgl1-mesa-glx libfreetype6-dev \
    && rm -rf /var/lib/apt/lists/*

# Instalar herramientas de Python
RUN pip3 install --no-cache-dir \
    shodan \
    theHarvester \
    sublist3r \
    dnsrecon \
    wafw00f \
    sqlmap \
    flask \
    fpdf \
    pillow \
    matplotlib \
    numpy \
    requests \
    werkzeug

# Instalar WPScan desde gem
RUN apt-get update && apt-get install -y ruby ruby-dev \
    && gem install wpscan \
    && rm -rf /var/lib/apt/lists/*

# Clonar y configurar herramientas específicas desde GitHub
RUN mkdir -p /opt/tools \
    # CMSeeK
    && git clone https://github.com/Tuhinshubhra/CMSeeK.git /opt/tools/CMSeeK \
    && pip3 install -r /opt/tools/CMSeeK/requirements.txt \
    # Joomscan
    && git clone https://github.com/rezasp/joomscan.git /opt/tools/joomscan \
    # Crear directorios necesarios
    && mkdir -p /app/temp/metagoofil /app/temp/photon /app/temp/aquatone /app/reports

# Configurar variable de entorno para PATH
ENV PATH="${PATH}:/opt/tools/joomscan"

# Crear usuario no root para ejecutar la aplicación
RUN groupadd -r osintuser && useradd -r -g osintuser osintuser
RUN chown -R osintuser:osintuser /app /opt/tools

# Copiar la aplicación
COPY . /app/

# Configurar directorio de trabajo
WORKDIR /app

# Puerto para exponer
EXPOSE 5001

# Cambiar al usuario no root
USER osintuser

# Comando para iniciar la aplicación
CMD ["python3", "osintdesk/app.py"] 