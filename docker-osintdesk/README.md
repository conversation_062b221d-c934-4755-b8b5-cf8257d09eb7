# OSINTdesk Docker

Este repositorio contiene la configuración Docker para ejecutar OSINTdesk con todas sus herramientas completamente integradas.

## Requisitos previos

- Docker instalado ([Guía de instalación de Docker](https://docs.docker.com/get-docker/))
- Docker Compose instalado ([Guía de instalación de Docker Compose](https://docs.docker.com/compose/install/))
- Al menos 4GB de RAM disponible para el contenedor
- ~5GB de espacio en disco para la imagen completa

## Instalación y configuración

1. Clona este repositorio o copia los archivos `Dockerfile` y `docker-compose.yml` a tu proyecto OSINTdesk.

2. **Construir la imagen Docker**:

   ```bash
   cd docker-osintdesk
   docker-compose build
   ```

   Este proceso puede tomar 15-30 minutos dependiendo de tu conexión a internet, ya que necesita descargar e instalar todas las herramientas de seguridad.

3. **Opcionalmente: Configura API keys**

   Edita el archivo `docker-compose.yml` y añade tus claves API como variables de entorno:

   ```yaml
   environment:
     - SHODAN_API_KEY=tu_clave_shodan
     - VIRUSTOTAL_API_KEY=tu_clave_virustotal
   ```

## Ejecución

Para iniciar OSINTdesk:

```bash
cd docker-osintdesk
docker-compose up
```

Para ejecutarlo en segundo plano (modo demonio):

```bash
docker-compose up -d
```

La aplicación estará disponible en: http://localhost:5001

## Acceso desde otros dispositivos/internet

### Opción 1: Exposición directa (sólo para redes de confianza)

Modifica el archivo `docker-compose.yml` para cambiar el mapeo de puertos:

```yaml
ports:
  - "0.0.0.0:5001:5001"
```

### Opción 2: Uso con ngrok

1. Instala ngrok
2. Ejecuta OSINTdesk con Docker
3. En otra terminal, ejecuta:
   ```bash
   ngrok http 5001
   ```
4. Ngrok te proporcionará una URL pública

## Herramientas incluidas

El contenedor incluye las siguientes herramientas preinstaladas:

- Herramientas básicas: whois, nslookup, dig, host
- Escáneres de red: nmap, sslscan
- Detección de WAF: wafw00f
- Enumeración de subdominios: sublist3r, dnsrecon
- Cosecha de emails: theHarvester
- Análisis de hosts: Shodan CLI
- Escáneres de CMS: CMSeeK, WPScan, Joomscan
- Pruebas web: sqlmap, nikto
- Herramientas de fuerza bruta: hydra, medusa
- Análisis SSL/TLS: testssl.sh

## Solución de problemas

### Problemas de permisos

Si encuentras errores relacionados con permisos al acceder a los directorios montados:

```bash
# Asegúrate de que las carpetas existan
mkdir -p reports temp

# Otorga permisos adecuados
chmod -R 777 reports temp
```

### Recursos insuficientes

Si el contenedor se detiene abruptamente, puede ser debido a falta de recursos. Intenta aumentar la memoria disponible para Docker en la configuración de Docker Desktop.

### Herramientas que no funcionan

Algunas herramientas requieren permisos especiales o consideraciones adicionales. Consulta la documentación específica de cada herramienta.

## Mantenimiento

- **Actualizar imagen**: `docker-compose build --no-cache`
- **Ver logs**: `docker-compose logs -f`
- **Detener contenedor**: `docker-compose down`
- **Limpiar volúmenes y recursos no usados**: `docker system prune -a --volumes` 