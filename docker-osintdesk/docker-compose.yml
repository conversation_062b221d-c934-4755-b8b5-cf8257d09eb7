version: '3'

services:
  osintdesk:
    build: 
      context: ..
      dockerfile: docker-osintdesk/Dockerfile
    ports:
      - "5001:5001"
    volumes:
      - ../reports:/app/reports
      - ../temp:/app/temp
    environment:
      - FLASK_APP=osintdesk/app.py
      - FLASK_ENV=production
      # Añade tus API keys aquí si las tienes
      # - SHODAN_API_KEY=tu_clave_shodan
      # - VIRUSTOTAL_API_KEY=tu_clave_virustotal
    restart: unless-stopped
    # Para herramientas que requieren capacidades especiales de red
    cap_add:
      - NET_ADMIN
      - NET_RAW 