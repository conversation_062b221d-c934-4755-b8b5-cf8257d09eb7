#!/usr/bin/env python3
"""
Enhanced Examples for OSINT Framework
====================================

This file contains practical examples of how to use the OSINT framework
for different types of security analysis with improved error handling,
logging, and best practices.

IMPORTANT: Only use with your own domains or with explicit authorization.
"""

import sys
import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
import time

# Add current directory to path for framework import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from osint_framework import OSINTAnalyzer
from config import Config

# Configure logging for examples
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OSINTExamples:
    """Enhanced examples class with better structure and error handling"""

    def __init__(self):
        """Initialize examples with safe test domains"""
        self.safe_domains = [
            "example.com",      # IANA reserved domain
            "httpbin.org",      # HTTP testing service
            "jsonplaceholder.typicode.com",  # JSON API testing
        ]
        self.results_cache = {}

    def basic_analysis_example(self) -> Optional[OSINTAnalyzer]:
        """Example 1: Basic domain analysis with step-by-step execution"""
        print("🔍 Example 1: Basic Domain Analysis")
        print("=" * 50)

        domain = self.safe_domains[0]
        logger.info(f"Starting basic analysis for {domain}")

        try:
            # Create analyzer with proper error handling
            analyzer = OSINTAnalyzer(domain, authorized=True)

            # Execute analysis steps with progress tracking
            steps = [
                ("Basic domain info", analyzer.basic_domain_info),
                ("SSL/TLS analysis", analyzer.ssl_analysis),
                ("Robots.txt analysis", analyzer.robots_sitemap_analysis),
                ("Security headers", analyzer.security_headers_analysis),
                ("Technology detection", analyzer.technology_detection),
                ("Generate recommendations", analyzer.generate_recommendations),
            ]

            for step_name, step_func in steps:
                print(f"\n📋 {step_name}...")
                try:
                    step_func()
                    print(f"   ✅ {step_name} completed")
                except Exception as e:
                    print(f"   ⚠️  {step_name} failed: {e}")
                    logger.warning(f"Step '{step_name}' failed: {e}")

            # Generate final report
            print("\n📊 Generating final report...")
            report = analyzer.generate_report()

            # Display summary
            summary = report['executive_summary']
            print(f"\n✅ Analysis completed!")
            print(f"📊 Risk level: {summary['risk_level']}")
            print(f"📋 Total findings: {summary['total_findings']}")
            print(f"📄 Report saved: {analyzer.report_filename}")

            self.results_cache[domain] = analyzer
            return analyzer

        except Exception as e:
            logger.error(f"Basic analysis failed: {e}")
            print(f"❌ Analysis failed: {e}")
            return None

def ejemplo_analisis_por_fases():
    """Ejemplo de análisis ejecutando fases individuales"""
    print("\n" + "=" * 60)
    print("EJEMPLO 2: Análisis por Fases")
    print("=" * 60)
    
    domain = "example.com"
    analyzer = OSINTAnalyzer(domain, authorized=False)
    
    # Verificar autorización primero
    try:
        analyzer.check_authorization()
    except SystemExit:
        print("❌ Análisis cancelado - sin autorización")
        return None
    
    print(f"\n🔍 Analizando {domain} por fases...")
    
    # Fase 1: Reconocimiento
    print("\n--- FASE 1: RECONOCIMIENTO ---")
    dns_info = analyzer.basic_domain_info()
    ssl_info = analyzer.ssl_analysis()
    web_info = analyzer.robots_sitemap_analysis()
    
    # Fase 2: OSINT
    print("\n--- FASE 2: OSINT ---")
    subdomains = analyzer.subdomain_enumeration()
    technologies = analyzer.technology_detection()
    
    # Fase 3: Seguridad
    print("\n--- FASE 3: ANÁLISIS DE SEGURIDAD ---")
    security_headers = analyzer.security_headers_analysis()
    
    # Fase 4: Recomendaciones e Informe
    print("\n--- FASE 4: GENERACIÓN DE INFORME ---")
    recommendations = analyzer.generate_recommendations()
    report = analyzer.generate_report()
    
    return report

def ejemplo_generacion_informe_html():
    """Ejemplo de generación de informe HTML"""
    print("\n" + "=" * 60)
    print("EJEMPLO 3: Generación de Informe HTML")
    print("=" * 60)
    
    # Buscar archivos JSON de informes existentes
    import glob
    json_files = glob.glob("osint_report_*.json")
    
    if not json_files:
        print("⚠️  No se encontraron informes JSON. Ejecute primero un análisis.")
        return
    
    # Usar el informe más reciente
    latest_report = max(json_files, key=os.path.getctime)
    print(f"📄 Generando informe HTML desde: {latest_report}")
    
    try:
        generator = ReportGenerator(latest_report)
        html_file = generator.generate_html_report()
        print(f"✅ Informe HTML generado: {html_file}")
    except Exception as e:
        print(f"❌ Error generando informe HTML: {e}")

def ejemplo_analisis_personalizado():
    """Ejemplo de análisis personalizado con configuraciones específicas"""
    print("\n" + "=" * 60)
    print("EJEMPLO 4: Análisis Personalizado")
    print("=" * 60)
    
    domain = "example.com"
    analyzer = OSINTAnalyzer(domain, authorized=False)
    
    # Configurar timeouts personalizados
    import requests
    
    print(f"🔧 Configurando análisis personalizado para {domain}")
    
    # Ejemplo de análisis específico de cabeceras de seguridad
    try:
        analyzer.check_authorization()
        
        print("\n🛡️  Análisis detallado de cabeceras de seguridad...")
        
        # Obtener cabeceras completas
        response = requests.get(f"https://{domain}", timeout=10)
        headers = response.headers
        
        # Análisis personalizado de cabeceras
        security_analysis = {
            'all_headers': dict(headers),
            'security_score': 0,
            'recommendations': []
        }
        
        # Evaluar cabeceras críticas
        critical_headers = [
            'Content-Security-Policy',
            'Strict-Transport-Security',
            'X-Frame-Options',
            'X-Content-Type-Options',
            'Referrer-Policy'
        ]
        
        for header in critical_headers:
            if header in headers:
                security_analysis['security_score'] += 20
                print(f"✅ {header}: {headers[header]}")
            else:
                security_analysis['recommendations'].append(f"Implementar {header}")
                print(f"❌ {header}: Ausente")
        
        print(f"\n📊 Puntuación de seguridad: {security_analysis['security_score']}/100")
        
        if security_analysis['recommendations']:
            print("\n📋 Recomendaciones:")
            for rec in security_analysis['recommendations']:
                print(f"  • {rec}")
        
    except Exception as e:
        print(f"❌ Error en análisis personalizado: {e}")

def mostrar_menu():
    """Mostrar menú de opciones"""
    print("\n" + "=" * 60)
    print("🔍 OSINT Framework - Ejemplos de Uso")
    print("=" * 60)
    print("1. Análisis Básico Completo")
    print("2. Análisis por Fases Individuales")
    print("3. Generar Informe HTML")
    print("4. Análisis Personalizado")
    print("5. Salir")
    print("=" * 60)

def main():
    """Función principal con menú interactivo"""
    
    # Mostrar aviso ético
    print("⚠️  AVISO ÉTICO IMPORTANTE:")
    print("Este framework solo debe usarse con autorización explícita del propietario del dominio.")
    print("Solo proceda si tiene autorización o está usando dominios de prueba públicos.")
    
    while True:
        mostrar_menu()
        
        try:
            opcion = input("\nSeleccione una opción (1-5): ").strip()
            
            if opcion == "1":
                ejemplo_analisis_basico()
            elif opcion == "2":
                ejemplo_analisis_por_fases()
            elif opcion == "3":
                ejemplo_generacion_informe_html()
            elif opcion == "4":
                ejemplo_analisis_personalizado()
            elif opcion == "5":
                print("\n👋 ¡Hasta luego!")
                break
            else:
                print("❌ Opción inválida. Seleccione 1-5.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Análisis interrumpido por el usuario. ¡Hasta luego!")
            break
        except Exception as e:
            print(f"❌ Error inesperado: {e}")
        
        input("\nPresione Enter para continuar...")

if __name__ == "__main__":
    main() 