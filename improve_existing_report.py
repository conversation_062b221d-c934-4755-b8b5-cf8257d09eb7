#!/usr/bin/env python3
"""
Script para mejorar reportes existentes con recomendaciones mejoradas
"""

import sys
import os
import json
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from osint_framework import OSINTAnalyzer

def improve_existing_report(report_filename):
    """Mejora un reporte existente con recomendaciones mejoradas"""
    
    print(f"🔧 Mejorando reporte: {report_filename}")
    
    try:
        # Cargar reporte existente
        with open(report_filename, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        print(f"✅ Reporte cargado exitosamente")
        
        # Crear analizador temporal para generar recomendaciones mejoradas
        domain = report_data.get('domain', 'unknown.com')
        analyzer = OSINTAnalyzer(domain, authorized=True)
        
        # Copiar datos existentes al analizador
        analyzer.results = report_data.copy()
        
        # Generar recomendaciones mejoradas
        print("🔍 Generando recomendaciones mejoradas...")
        improved_recommendations = analyzer.generate_recommendations()
        
        # Actualizar el reporte con las nuevas recomendaciones
        report_data['recommendations'] = improved_recommendations
        
        # Actualizar timestamp
        report_data['timestamp'] = datetime.now().isoformat()
        
        # Actualizar resumen ejecutivo si existe
        if 'executive_summary' in report_data:
            report_data['executive_summary']['total_findings'] = len(improved_recommendations)
            report_data['executive_summary']['last_updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Crear nombre de archivo mejorado
        base_name = report_filename.replace('.json', '')
        improved_filename = f"{base_name}_improved.json"
        
        # Guardar reporte mejorado
        with open(improved_filename, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Reporte mejorado guardado: {improved_filename}")
        print(f"📊 Recomendaciones generadas: {len(improved_recommendations)}")
        
        # Mostrar resumen de recomendaciones
        priority_count = {}
        for rec in improved_recommendations:
            priority = rec.get('priority', 'Unknown')
            priority_count[priority] = priority_count.get(priority, 0) + 1
        
        print(f"\n📋 Resumen de recomendaciones:")
        for priority, count in priority_count.items():
            emoji = {'Alta': '🔴', 'Media': '🟡', 'Baja': '🟢'}.get(priority, '⚪')
            print(f"   {emoji} {priority}: {count}")
        
        print(f"\n🌐 Visualizar en: http://localhost:8080/visualize/{improved_filename}")
        
        return improved_filename
        
    except FileNotFoundError:
        print(f"❌ Error: No se encontró el archivo {report_filename}")
        return None
    except json.JSONDecodeError:
        print(f"❌ Error: El archivo {report_filename} no es un JSON válido")
        return None
    except Exception as e:
        print(f"❌ Error inesperado: {e}")
        return None

def list_available_reports():
    """Lista todos los reportes disponibles"""
    print("📁 Reportes disponibles:")
    
    json_files = [f for f in os.listdir('.') if f.startswith('osint_report_') and f.endswith('.json')]
    
    if not json_files:
        print("   No se encontraron reportes")
        return []
    
    for i, filename in enumerate(json_files, 1):
        # Extraer información del nombre del archivo
        parts = filename.replace('osint_report_', '').replace('.json', '').split('_')
        if len(parts) >= 3:
            domain = parts[0]
            date = parts[1]
            time = parts[2]
            print(f"   {i}. {domain} - {date} {time}")
        else:
            print(f"   {i}. {filename}")
    
    return json_files

def main():
    """Función principal"""
    print("🔧 Mejorador de Reportes OSINT")
    print("=" * 50)
    
    # Listar reportes disponibles
    available_reports = list_available_reports()
    
    if not available_reports:
        print("\n❌ No hay reportes para mejorar")
        return
    
    # Buscar específicamente el reporte de karedesk.com
    karedesk_reports = [f for f in available_reports if 'karedesk.com' in f]
    
    if karedesk_reports:
        # Usar el más reciente
        latest_report = sorted(karedesk_reports)[-1]
        print(f"\n🎯 Mejorando reporte más reciente de karedesk.com: {latest_report}")
        
        improved_file = improve_existing_report(latest_report)
        
        if improved_file:
            print(f"\n🎉 ¡Reporte mejorado exitosamente!")
            print(f"📄 Archivo original: {latest_report}")
            print(f"📄 Archivo mejorado: {improved_file}")
            print(f"\n🌐 Abrir en navegador: http://localhost:8080/visualize/{improved_file}")
        
    else:
        print(f"\n❌ No se encontraron reportes de karedesk.com")
        print(f"💡 Reportes disponibles: {len(available_reports)}")
        
        if available_reports:
            print(f"\n¿Desea mejorar otro reporte? Reportes disponibles:")
            for i, report in enumerate(available_reports, 1):
                print(f"   {i}. {report}")

if __name__ == "__main__":
    main()
