#!/bin/bash

# Script de instalación de herramientas avanzadas OSINT
# =====================================================

echo "🚀 Instalador de Herramientas Avanzadas OSINT"
echo "============================================="
echo ""
echo "⚠️  IMPORTANTE: Este script instalará herramientas de seguridad."
echo "   Solo use estas herramientas con autorización explícita."
echo ""
read -p "¿Desea continuar? (s/n): " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Ss]$ ]]; then
    echo "❌ Instalación cancelada."
    exit 1
fi

# Detectar sistema operativo
OS="Unknown"
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="Linux"
    if [ -f /etc/debian_version ]; then
        OS="Debian"
    elif [ -f /etc/redhat-release ]; then
        OS="RedHat"
    fi
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macOS"
fi

echo "🔍 Sistema detectado: $OS"
echo ""

# Función para verificar si un comando existe
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Función para instalar en macOS
install_macos() {
    echo "📦 Instalando herramientas para macOS..."
    
    # Verificar Homebrew
    if ! command_exists brew; then
        echo "❌ Homebrew no está instalado. Instálelo primero:"
        echo "   /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        exit 1
    fi
    
    # Actualizar Homebrew
    echo "🔄 Actualizando Homebrew..."
    brew update
    
    # Instalar herramientas básicas
    echo "📦 Instalando herramientas básicas..."
    brew install nmap
    brew install gobuster
    brew install nikto
    brew install whatweb
    
    # Instalar Go (necesario para algunas herramientas)
    if ! command_exists go; then
        echo "📦 Instalando Go..."
        brew install go
    fi
    
    # Configurar GOPATH
    export GOPATH=$HOME/go
    export PATH=$PATH:$GOPATH/bin
    echo "export GOPATH=$HOME/go" >> ~/.zshrc
    echo "export PATH=\$PATH:\$GOPATH/bin" >> ~/.zshrc
}

# Función para instalar en Linux
install_linux() {
    echo "📦 Instalando herramientas para Linux..."
    
    # Actualizar repositorios
    if [ "$OS" == "Debian" ]; then
        sudo apt-get update
        PKG_MANAGER="apt-get"
        INSTALL_CMD="sudo apt-get install -y"
    else
        sudo yum update -y
        PKG_MANAGER="yum"
        INSTALL_CMD="sudo yum install -y"
    fi
    
    # Instalar herramientas básicas
    echo "📦 Instalando herramientas básicas..."
    $INSTALL_CMD nmap
    $INSTALL_CMD nikto
    $INSTALL_CMD gobuster
    
    # Instalar Go si no está presente
    if ! command_exists go; then
        echo "📦 Instalando Go..."
        wget https://go.dev/dl/go1.21.0.linux-amd64.tar.gz
        sudo tar -C /usr/local -xzf go1.21.0.linux-amd64.tar.gz
        rm go1.21.0.linux-amd64.tar.gz
        export PATH=$PATH:/usr/local/go/bin
        echo "export PATH=\$PATH:/usr/local/go/bin" >> ~/.bashrc
    fi
    
    # Configurar GOPATH
    export GOPATH=$HOME/go
    export PATH=$PATH:$GOPATH/bin
    echo "export GOPATH=$HOME/go" >> ~/.bashrc
    echo "export PATH=\$PATH:\$GOPATH/bin" >> ~/.bashrc
}

# Instalar herramientas Go comunes
install_go_tools() {
    echo ""
    echo "📦 Instalando herramientas basadas en Go..."
    
    # Crear directorio Go si no existe
    mkdir -p $HOME/go/bin
    
    # Nuclei
    echo "🎯 Instalando Nuclei..."
    go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest
    
    # Subfinder
    echo "🔍 Instalando Subfinder..."
    go install -v github.com/projectdiscovery/subfinder/v2/cmd/subfinder@latest
    
    # HTTPX
    echo "🌐 Instalando HTTPX..."
    go install -v github.com/projectdiscovery/httpx/cmd/httpx@latest
    
    # Amass
    echo "🕸️ Instalando Amass..."
    go install -v github.com/owasp-amass/amass/v4/...@master
    
    # Waybackurls
    echo "📚 Instalando Waybackurls..."
    go install github.com/tomnomnom/waybackurls@latest
    
    # FFUF
    echo "🔨 Instalando FFUF..."
    go install github.com/ffuf/ffuf/v2@latest
}

# Actualizar templates de Nuclei
update_nuclei_templates() {
    echo ""
    echo "📋 Actualizando templates de Nuclei..."
    nuclei -update-templates
}

# Instalar dependencias Python adicionales
install_python_deps() {
    echo ""
    echo "🐍 Instalando dependencias Python adicionales..."
    
    # Activar entorno virtual si existe
    if [ -d "osint_env" ]; then
        source osint_env/bin/activate
    fi
    
    pip install shodan censys python-whois
}

# Crear directorio de wordlists
setup_wordlists() {
    echo ""
    echo "📝 Configurando wordlists..."
    
    WORDLIST_DIR="/usr/share/wordlists"
    
    if [ "$OS" == "macOS" ]; then
        WORDLIST_DIR="$HOME/wordlists"
        mkdir -p $WORDLIST_DIR
    fi
    
    # Descargar wordlists comunes si no existen
    if [ ! -d "$WORDLIST_DIR/dirb" ]; then
        echo "📥 Descargando wordlists de dirb..."
        git clone https://github.com/v0re/dirb.git /tmp/dirb
        if [ "$OS" == "macOS" ]; then
            cp -r /tmp/dirb/wordlists $WORDLIST_DIR/dirb
        else
            sudo cp -r /tmp/dirb/wordlists $WORDLIST_DIR/dirb
        fi
        rm -rf /tmp/dirb
    fi
}

# Verificar instalación
verify_installation() {
    echo ""
    echo "✅ Verificando instalación..."
    echo ""
    
    tools=("nuclei" "nmap" "subfinder" "httpx" "amass" "waybackurls" "gobuster" "ffuf")
    
    for tool in "${tools[@]}"; do
        if command_exists $tool; then
            echo "✅ $tool instalado correctamente"
        else
            echo "❌ $tool no se pudo instalar"
        fi
    done
}

# Mostrar información de configuración
show_config_info() {
    echo ""
    echo "📋 Configuración adicional recomendada:"
    echo ""
    echo "1. API Keys (opcional pero recomendado):"
    echo "   - Shodan: https://account.shodan.io/register"
    echo "   - Censys: https://censys.io/register"
    echo ""
    echo "2. Para usar el análisis avanzado:"
    echo "   python web_portal.py"
    echo "   # O desde línea de comandos:"
    echo "   python advanced_osint_framework.py ejemplo.com --authorized"
    echo ""
    echo "3. Recuerde siempre:"
    echo "   ⚠️  Solo use estas herramientas con autorización explícita"
    echo "   ⚠️  Respete los términos de servicio de cada sitio"
    echo "   ⚠️  Use rate limiting para evitar bloqueos"
}

# Ejecutar instalación según el sistema
echo ""
if [ "$OS" == "macOS" ]; then
    install_macos
elif [ "$OS" == "Linux" ] || [ "$OS" == "Debian" ] || [ "$OS" == "RedHat" ]; then
    install_linux
else
    echo "❌ Sistema operativo no soportado: $OS"
    echo "   Por favor, instale las herramientas manualmente."
    exit 1
fi

# Instalar herramientas Go
install_go_tools

# Actualizar templates de Nuclei
if command_exists nuclei; then
    update_nuclei_templates
fi

# Instalar dependencias Python
install_python_deps

# Configurar wordlists
setup_wordlists

# Verificar instalación
verify_installation

# Mostrar información de configuración
show_config_info

echo ""
echo "🎉 ¡Instalación completada!"
echo ""
echo "⚡ Para comenzar a usar el portal web avanzado:"
echo "   ./start_portal.sh"
echo "" 