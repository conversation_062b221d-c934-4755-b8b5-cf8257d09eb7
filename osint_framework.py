#!/usr/bin/env python3
"""
OSINT Security Analysis Framework
==================================

IMPORTANTE: Este framework está diseñado para fines educativos y de seguridad ética.
Solo debe utilizarse con autorización explícita del propietario del dominio.

Características:
- Análisis OSINT responsable
- Recolección de información pública
- Generación de informes ejecutivos
- Verificaciones éticas y legales integradas

Autor: Security Analysis Framework
Versión: 1.0
"""

import sys
import os
import json
import subprocess
import requests
import socket
import ssl
import datetime
import argparse
from urllib.parse import urlparse
import concurrent.futures
import time
import re
import threading
import dns.resolver
import whois

class OSINTAnalyzer:
    def __init__(self, domain, authorized=False):
        self.domain = domain
        self.authorized = authorized
        self.results = {
            'domain': domain,
            'timestamp': datetime.datetime.now().isoformat(),
            'reconnaissance': {},
            'osint': {},
            'vulnerabilities': {},
            'recommendations': [],
            'ethical_notice': "Este análisis fue realizado únicamente con información públicamente disponible."
        }
        
        if not self.authorized:
            print("⚠️  AVISO ÉTICO: Asegúrese de tener autorización explícita antes de realizar cualquier análisis.")
            print("⚠️  Este framework solo debe usarse para fines educativos o con autorización del propietario.")
    
    def check_authorization(self):
        """Verificar autorización antes de proceder"""
        if not self.authorized:
            response = input(f"¿Tiene autorización explícita para analizar {self.domain}? (si/no): ")
            if response.lower() not in ['si', 'sí', 'yes', 'y']:
                print("❌ Análisis cancelado. Solo proceda con autorización explícita.")
                sys.exit(1)
            self.authorized = True
    
    def basic_domain_info(self):
        """Fase 1.1: Información básica de dominio usando información pública"""
        print("\n🔍 Fase 1.1: Recolectando información básica de dominio...")
        
        # Información DNS mejorada
        try:
            ip = socket.gethostbyname(self.domain)
            
            # Información DNS detallada
            dns_info = {
                'ip_address': ip,
                'a_records': [],
                'mx_records': [],
                'txt_records': [],
                'ns_records': [],
                'cname_records': [],
                'soa_record': None
            }
            
            resolver = dns.resolver.Resolver()
            
            # Registros A
            try:
                for rdata in resolver.resolve(self.domain, 'A'):
                    dns_info['a_records'].append(str(rdata))
            except:
                pass
            
            # Registros MX
            try:
                for rdata in resolver.resolve(self.domain, 'MX'):
                    dns_info['mx_records'].append(f"{rdata.preference} {rdata.exchange}")
            except:
                pass
            
            # Registros TXT
            try:
                for rdata in resolver.resolve(self.domain, 'TXT'):
                    dns_info['txt_records'].append(str(rdata))
            except:
                pass
            
            # Registros NS
            try:
                for rdata in resolver.resolve(self.domain, 'NS'):
                    dns_info['ns_records'].append(str(rdata))
            except:
                pass
            
            # Registro SOA
            try:
                soa = resolver.resolve(self.domain, 'SOA')[0]
                dns_info['soa_record'] = {
                    'mname': str(soa.mname),
                    'rname': str(soa.rname),
                    'serial': soa.serial,
                    'refresh': soa.refresh,
                    'retry': soa.retry,
                    'expire': soa.expire,
                    'minimum': soa.minimum
                }
            except:
                pass
            
            self.results['reconnaissance']['dns_info'] = dns_info
            print(f"✓ IP Address: {ip}")
            print(f"✓ Registros DNS: A({len(dns_info['a_records'])}), MX({len(dns_info['mx_records'])}), TXT({len(dns_info['txt_records'])})")
            
        except Exception as e:
            print(f"❌ Error obteniendo información DNS: {e}")
        
        # Información WHOIS
        try:
            w = whois.whois(self.domain)
            whois_info = {
                'registrar': w.registrar,
                'creation_date': str(w.creation_date) if w.creation_date else None,
                'expiration_date': str(w.expiration_date) if w.expiration_date else None,
                'name_servers': w.name_servers if w.name_servers else [],
                'status': w.status if w.status else [],
                'country': w.country,
                'organization': w.org
            }
            self.results['reconnaissance']['whois'] = whois_info
            print(f"✓ Registrar: {whois_info['registrar']}")
            if whois_info['creation_date']:
                print(f"✓ Fecha de creación: {whois_info['creation_date'][:10]}")
        except Exception as e:
            print(f"⚠️  No se pudo obtener información WHOIS: {e}")
        
        # Geolocalización básica de IP
        try:
            geo_response = requests.get(f"http://ipapi.co/{ip}/json/", timeout=5)
            if geo_response.status_code == 200:
                geo_data = geo_response.json()
                geo_info = {
                    'country': geo_data.get('country_name'),
                    'region': geo_data.get('region'),
                    'city': geo_data.get('city'),
                    'isp': geo_data.get('org'),
                    'timezone': geo_data.get('timezone')
                }
                self.results['reconnaissance']['geolocation'] = geo_info
                print(f"✓ Ubicación: {geo_info['city']}, {geo_info['country']}")
                print(f"✓ ISP: {geo_info['isp']}")
        except:
            pass
    
    def ssl_analysis(self):
        """Fase 1.2: Análisis SSL/TLS mejorado"""
        print("\n🔒 Fase 1.2: Analizando configuración SSL/TLS...")
        
        ssl_info = {}
        try:
            context = ssl.create_default_context()
            with socket.create_connection((self.domain, 443), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=self.domain) as ssock:
                    cert = ssock.getpeercert()
                    
                    ssl_info = {
                        'ssl_version': ssock.version(),
                        'cipher_suite': str(ssock.cipher()[0]) if ssock.cipher() else None,
                        'issuer': dict(x[0] for x in cert['issuer']),
                        'subject': dict(x[0] for x in cert['subject']),
                        'expiry_date': cert['notAfter'],
                        'start_date': cert['notBefore'],
                        'san': [],
                        'serial_number': cert.get('serialNumber'),
                        'signature_algorithm': cert.get('signatureAlgorithm')
                    }
                    
                    # Subject Alternative Names
                    for ext in cert.get('subjectAltName', []):
                        if ext[0] == 'DNS':
                            ssl_info['san'].append(ext[1])
                    
                    print(f"✓ SSL Version: {ssl_info['ssl_version']}")
                    print(f"✓ Cipher: {ssl_info['cipher_suite']}")
                    print(f"✓ Emisor: {ssl_info['issuer'].get('organizationName', 'Desconocido')}")
                    print(f"✓ Válido hasta: {ssl_info['expiry_date']}")
                    print(f"✓ SANs encontrados: {len(ssl_info['san'])}")
                    
        except Exception as e:
            print(f"❌ Error en análisis SSL: {e}")
        
        self.results['reconnaissance']['ssl_info'] = ssl_info
        return ssl_info
    
    def robots_sitemap_analysis(self):
        """Fase 1.3: Análisis de robots.txt y sitemap.xml mejorado"""
        print("\n🤖 Fase 1.3: Analizando robots.txt y sitemap.xml...")
        
        web_info = {}
        
        # Analizar robots.txt
        try:
            response = requests.get(f"https://{self.domain}/robots.txt", timeout=10)
            if response.status_code == 200:
                robots_content = response.text
                web_info['robots_txt'] = robots_content
                
                # Extraer información útil de robots.txt
                disallow_paths = re.findall(r'Disallow:\s*(.+)', robots_content)
                sitemap_urls = re.findall(r'Sitemap:\s*(.+)', robots_content)
                
                web_info['robots_analysis'] = {
                    'disallow_paths': disallow_paths[:10],  # Primeros 10
                    'sitemap_urls': sitemap_urls,
                    'user_agents': re.findall(r'User-agent:\s*(.+)', robots_content)
                }
                
                print(f"✓ robots.txt encontrado ({len(disallow_paths)} rutas bloqueadas)")
                if sitemap_urls:
                    print(f"✓ {len(sitemap_urls)} sitemaps referenciados")
            else:
                print("⚠️  robots.txt no encontrado")
        except Exception as e:
            print(f"❌ Error accediendo a robots.txt: {e}")
        
        # Analizar sitemap.xml
        try:
            response = requests.get(f"https://{self.domain}/sitemap.xml", timeout=10)
            if response.status_code == 200:
                sitemap_content = response.text
                
                # Extraer URLs del sitemap
                urls = re.findall(r'<loc>(.*?)</loc>', sitemap_content)
                web_info['sitemap_xml'] = sitemap_content[:2000]  # Primeros 2000 caracteres
                web_info['sitemap_urls'] = urls[:20]  # Primeras 20 URLs
                
                print(f"✓ sitemap.xml encontrado ({len(urls)} URLs)")
            else:
                print("⚠️  sitemap.xml no encontrado")
        except Exception as e:
            print(f"❌ Error accediendo a sitemap.xml: {e}")
        
        # Buscar archivos comunes adicionales
        common_files = [
            'security.txt', '.well-known/security.txt',
            'humans.txt', 'ads.txt', 'app-ads.txt'
        ]
        
        found_files = {}
        for file_path in common_files:
            try:
                response = requests.get(f"https://{self.domain}/{file_path}", timeout=5)
                if response.status_code == 200:
                    found_files[file_path] = response.text[:500]  # Primeros 500 caracteres
                    print(f"✓ {file_path} encontrado")
            except:
                pass
        
        if found_files:
            web_info['additional_files'] = found_files
        
        self.results['reconnaissance']['web_info'] = web_info
        return web_info
    
    def subdomain_enumeration(self):
        """Fase 2.2: Enumeración de subdominios mejorada"""
        print("\n🌐 Fase 2.2: Enumerando subdominios públicos...")
        
        subdomains = set()
        subdomain_sources = {}
        
        # Certificate Transparency
        try:
            print("🔍 Buscando en Certificate Transparency...")
            url = f"https://crt.sh/?q=%.{self.domain}&output=json"
            response = requests.get(url, timeout=15)
            if response.status_code == 200:
                data = response.json()
                ct_subdomains = set()
                for entry in data:
                    name_value = entry.get('name_value', '')
                    for subdomain in name_value.split('\n'):
                        clean_subdomain = subdomain.strip()
                        if clean_subdomain.endswith(self.domain) and clean_subdomain != self.domain:
                            ct_subdomains.add(clean_subdomain)
                            subdomains.add(clean_subdomain)
                
                subdomain_sources['certificate_transparency'] = list(ct_subdomains)
                print(f"✓ Certificate Transparency: {len(ct_subdomains)} subdominios")
        except Exception as e:
            print(f"❌ Error en Certificate Transparency: {e}")
        
        # CommonCrawl
        try:
            print("🔍 Buscando en CommonCrawl...")
            cc_url = f"http://index.commoncrawl.org/CC-MAIN-2024-10-index?url=*.{self.domain}&output=json"
            response = requests.get(cc_url, timeout=10)
            if response.status_code == 200:
                cc_subdomains = set()
                for line in response.text.strip().split('\n')[:100]:  # Limitar a 100 resultados
                    try:
                        data = json.loads(line)
                        url = data.get('url', '')
                        domain_match = re.search(r'https?://([^/]+)', url)
                        if domain_match:
                            found_domain = domain_match.group(1)
                            if found_domain.endswith(self.domain) and found_domain != self.domain:
                                cc_subdomains.add(found_domain)
                                subdomains.add(found_domain)
                    except:
                        continue
                
                subdomain_sources['commoncrawl'] = list(cc_subdomains)
                print(f"✓ CommonCrawl: {len(cc_subdomains)} subdominios")
        except Exception as e:
            print(f"⚠️  CommonCrawl no disponible: {e}")
        
        # Búsqueda DNS con wordlist básica
        try:
            print("🔍 Probando subdominios comunes...")
            common_subdomains = [
                'www', 'mail', 'ftp', 'admin', 'test', 'dev', 'staging', 'api',
                'blog', 'shop', 'support', 'help', 'docs', 'cdn', 'static',
                'img', 'images', 'app', 'mobile', 'secure', 'vpn', 'remote'
            ]
            
            dns_subdomains = set()
            for sub in common_subdomains:
                try:
                    full_domain = f"{sub}.{self.domain}"
                    socket.gethostbyname(full_domain)
                    dns_subdomains.add(full_domain)
                    subdomains.add(full_domain)
                except:
                    pass
            
            subdomain_sources['dns_bruteforce'] = list(dns_subdomains)
            print(f"✓ DNS Bruteforce: {len(dns_subdomains)} subdominios")
        except Exception as e:
            print(f"❌ Error en DNS bruteforce: {e}")
        
        self.results['osint']['subdomains'] = list(subdomains)
        self.results['osint']['subdomain_sources'] = subdomain_sources
        print(f"📊 Total de subdominios únicos encontrados: {len(subdomains)}")
        
        return list(subdomains)
    
    def port_scanning_basic(self):
        """Escaneo básico de puertos comunes"""
        print("\n🔌 Fase 2.3: Escaneando puertos comunes...")
        
        common_ports = [21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 8080, 8443]
        open_ports = []
        
        def scan_port(port):
            try:
                with socket.create_connection((self.domain, port), timeout=2):
                    return port
            except:
                return None
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            future_to_port = {executor.submit(scan_port, port): port for port in common_ports}
            for future in concurrent.futures.as_completed(future_to_port):
                result = future.result()
                if result:
                    open_ports.append(result)
        
        if open_ports:
            print(f"✓ Puertos abiertos encontrados: {', '.join(map(str, sorted(open_ports)))}")
        else:
            print("ℹ️  No se encontraron puertos comunes abiertos")
        
        self.results['reconnaissance']['open_ports'] = sorted(open_ports)
        return open_ports
    
    def security_headers_analysis(self):
        """Fase 3.3: Análisis de cabeceras de seguridad mejorado"""
        print("\n🛡️  Fase 3.3: Analizando cabeceras de seguridad...")
        
        security_headers = {}
        response_info = {}
        
        try:
            response = requests.get(f"https://{self.domain}", timeout=10, allow_redirects=True)
            headers = response.headers
            
            # Información adicional de la respuesta
            response_info = {
                'status_code': response.status_code,
                'content_type': headers.get('Content-Type', ''),
                'content_length': headers.get('Content-Length', ''),
                'server': headers.get('Server', ''),
                'date': headers.get('Date', ''),
                'last_modified': headers.get('Last-Modified', ''),
                'etag': headers.get('ETag', ''),
                'final_url': response.url
            }
            
            security_header_list = [
                'Content-Security-Policy',
                'X-XSS-Protection',
                'X-Frame-Options',
                'Strict-Transport-Security',
                'Referrer-Policy',
                'Permissions-Policy',
                'X-Content-Type-Options',
                'Feature-Policy',
                'Expect-CT',
                'X-Permitted-Cross-Domain-Policies'
            ]
            
            for header in security_header_list:
                if header in headers:
                    security_headers[header] = headers[header]
                    print(f"✓ {header}: Presente")
                else:
                    security_headers[header] = "Ausente"
                    print(f"⚠️  {header}: Ausente")
            
            # Análisis adicional de cookies
            cookies_info = []
            for cookie in response.cookies:
                cookie_info = {
                    'name': cookie.name,
                    'secure': cookie.secure,
                    'httponly': hasattr(cookie, 'has_nonstandard_attr') and cookie.has_nonstandard_attr('HttpOnly'),
                    'samesite': getattr(cookie, 'get_nonstandard_attr', lambda x, default=None: default)('SameSite')
                }
                cookies_info.append(cookie_info)
            
            if cookies_info:
                response_info['cookies'] = cookies_info
                print(f"ℹ️  Encontradas {len(cookies_info)} cookies")
        
        except Exception as e:
            print(f"❌ Error analizando cabeceras: {e}")
        
        self.results['vulnerabilities']['security_headers'] = security_headers
        self.results['reconnaissance']['response_info'] = response_info
        return security_headers
    
    def technology_detection(self):
        """Detectar tecnologías web mejorado"""
        print("\n💻 Detectando tecnologías web...")
        
        technologies = {}
        try:
            response = requests.get(f"https://{self.domain}", timeout=10)
            headers = response.headers
            content = response.text[:10000]  # Primeros 10000 caracteres
            
            # Detectar servidor web
            if 'Server' in headers:
                technologies['web_server'] = headers['Server']
                print(f"✓ Servidor Web: {technologies['web_server']}")
            
            # Detectar tecnologías por headers
            header_techs = {}
            tech_headers = {
                'X-Powered-By': 'framework',
                'X-Generator': 'cms',
                'X-Drupal-Cache': 'Drupal',
                'X-Pingback': 'WordPress'
            }
            
            for header, tech in tech_headers.items():
                if header in headers:
                    header_techs[header] = headers[header]
                    print(f"✓ {header}: {headers[header]}")
            
            # Detectar tecnologías comunes en contenido
            tech_indicators = {
                'WordPress': ['wp-content', 'wp-includes', 'wp-json'],
                'Drupal': ['drupal', 'sites/default', '/core/'],
                'Joomla': ['joomla', 'com_content', 'option=com_'],
                'React': ['react', 'ReactDOM', '__REACT'],
                'Angular': ['angular', 'ng-', 'AngularJS'],
                'Vue.js': ['vue.js', '__vue__', 'Vue.'],
                'jQuery': ['jquery', '$.fn.jquery', 'jQuery'],
                'Bootstrap': ['bootstrap', 'btn-', 'container-'],
                'Shopify': ['shopify', 'myshopify'],
                'Magento': ['magento', 'mage/'],
                'Laravel': ['laravel', 'laravel_session'],
                'Django': ['django', 'csrfmiddlewaretoken'],
                'Flask': ['flask', 'Werkzeug'],
                'Express': ['express', 'X-Powered-By: Express'],
                'Next.js': ['next.js', '_next/', '__NEXT_DATA__'],
                'Gatsby': ['gatsby', 'gatsby-'],
                'Cloudflare': ['cloudflare', '__cf_bm', 'cf-ray']
            }
            
            detected_techs = []
            for tech, indicators in tech_indicators.items():
                if any(indicator.lower() in content.lower() or indicator.lower() in str(headers).lower() for indicator in indicators):
                    detected_techs.append(tech)
            
            technologies['detected'] = detected_techs
            technologies['header_technologies'] = header_techs
            
            # JavaScript frameworks específicos
            js_frameworks = []
            js_patterns = {
                'React': r'react[.-](\d+\.\d+\.\d+)',
                'Angular': r'angular[.-](\d+\.\d+\.\d+)',
                'Vue': r'vue[.-](\d+\.\d+\.\d+)',
                'jQuery': r'jquery[.-](\d+\.\d+\.\d+)'
            }
            
            for framework, pattern in js_patterns.items():
                matches = re.findall(pattern, content.lower())
                if matches:
                    js_frameworks.append(f"{framework} {matches[0]}")
            
            if js_frameworks:
                technologies['javascript_frameworks'] = js_frameworks
            
            print(f"✓ Tecnologías detectadas: {', '.join(detected_techs) if detected_techs else 'Ninguna específica'}")
            if js_frameworks:
                print(f"✓ Frameworks JS: {', '.join(js_frameworks)}")
            
        except Exception as e:
            print(f"❌ Error detectando tecnologías: {e}")
        
        self.results['reconnaissance']['technology'] = technologies
        return technologies
    
    def generate_recommendations(self):
        """Generar recomendaciones basadas en hallazgos"""
        recommendations = []
        
        # Recomendaciones basadas en cabeceras de seguridad
        security_headers = self.results.get('vulnerabilities', {}).get('security_headers', {})
        
        if security_headers.get('Content-Security-Policy') == 'Ausente':
            recommendations.append({
                'category': 'Cabeceras de Seguridad',
                'issue': 'Content-Security-Policy ausente',
                'recommendation': 'Implementar Content-Security-Policy para prevenir ataques XSS',
                'priority': 'Alta'
            })
        
        if security_headers.get('Strict-Transport-Security') == 'Ausente':
            recommendations.append({
                'category': 'Cabeceras de Seguridad',
                'issue': 'HSTS ausente',
                'recommendation': 'Implementar Strict-Transport-Security para forzar HTTPS',
                'priority': 'Media'
            })
        
        if security_headers.get('X-Frame-Options') == 'Ausente':
            recommendations.append({
                'category': 'Cabeceras de Seguridad',
                'issue': 'X-Frame-Options ausente',
                'recommendation': 'Implementar X-Frame-Options para prevenir clickjacking',
                'priority': 'Media'
            })
        
        # Recomendaciones generales
        recommendations.append({
            'category': 'Monitoreo',
            'issue': 'Monitoreo continuo',
            'recommendation': 'Implementar monitoreo continuo de seguridad y alertas de amenazas',
            'priority': 'Alta'
        })
        
        self.results['recommendations'] = recommendations
        return recommendations
    
    def generate_report(self):
        """Generar informe ejecutivo"""
        print("\n📊 Generando informe ejecutivo...")
        
        report = {
            'executive_summary': {
                'domain': self.domain,
                'analysis_date': self.results['timestamp'],
                'total_findings': len(self.results.get('recommendations', [])),
                'risk_level': self._calculate_risk_level()
            },
            'detailed_findings': self.results,
            'recommendations': self.results.get('recommendations', [])
        }
        
        # Guardar informe en JSON
        filename = f"osint_report_{self.domain}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Guardar el nombre del archivo como atributo para acceso posterior
        self.report_filename = filename
        
        print(f"✓ Informe guardado en: {filename}")
        
        # Generar informe HTML también
        try:
            from report_generator import ReportGenerator
            generator = ReportGenerator(filename)
            html_filename = generator.generate_html_report()
            print(f"✅ Informe HTML generado: {html_filename}")
        except Exception as e:
            print(f"⚠️  Error generando HTML: {e}")
        
        return report
    
    def _calculate_risk_level(self):
        """Calcular nivel de riesgo basado en hallazgos"""
        security_headers = self.results.get('vulnerabilities', {}).get('security_headers', {})
        missing_headers = sum(1 for v in security_headers.values() if v == 'Ausente')
        
        if missing_headers >= 5:
            return 'Alto'
        elif missing_headers >= 3:
            return 'Medio'
        else:
            return 'Bajo'
    
    def run_full_analysis(self):
        """Ejecutar análisis completo"""
        print(f"\n🚀 Iniciando análisis OSINT para: {self.domain}")
        print("=" * 60)
        
        # Verificar autorización
        self.check_authorization()
        
        # Ejecutar todas las fases
        try:
            self.basic_domain_info()
            self.ssl_analysis()
            self.robots_sitemap_analysis()
            self.subdomain_enumeration()
            self.port_scanning_basic()
            self.security_headers_analysis()
            self.technology_detection()
            self.generate_recommendations()
            
            # Generar informe final
            report = self.generate_report()
            
            print("\n" + "=" * 60)
            print("✅ Análisis completado exitosamente")
            print(f"📊 Nivel de riesgo evaluado: {report['executive_summary']['risk_level']}")
            print(f"📋 Total de recomendaciones: {report['executive_summary']['total_findings']}")
            
            return report
            
        except KeyboardInterrupt:
            print("\n❌ Análisis interrumpido por el usuario")
            sys.exit(1)
        except Exception as e:
            print(f"\n❌ Error durante el análisis: {e}")
            sys.exit(1)

def main():
    parser = argparse.ArgumentParser(description='OSINT Security Analysis Framework')
    parser.add_argument('domain', help='Dominio a analizar')
    parser.add_argument('--authorized', action='store_true', 
                       help='Confirmar que tiene autorización para el análisis')
    
    args = parser.parse_args()
    
    # Limpiar dominio (quitar https:// si está presente)
    domain = args.domain.replace('https://', '').replace('http://', '').replace('www.', '')
    
    analyzer = OSINTAnalyzer(domain, args.authorized)
    analyzer.run_full_analysis()

if __name__ == "__main__":
    main() 