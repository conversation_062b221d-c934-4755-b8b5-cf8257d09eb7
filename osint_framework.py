#!/usr/bin/env python3
"""
OSINT Security Analysis Framework
==================================

IMPORTANTE: Este framework está diseñado para fines educativos y de seguridad ética.
Solo debe utilizarse con autorización explícita del propietario del dominio.

Características:
- Análisis OSINT responsable
- Recolección de información pública
- Generación de informes ejecutivos
- Verificaciones éticas y legales integradas

Autor: Security Analysis Framework
Versión: 2.0
"""

import sys
import os
import json
import subprocess
import requests
import socket
import ssl
import datetime
import argparse
import logging
from urllib.parse import urlparse
import concurrent.futures
import time
import re
import threading
from typing import Dict, List, Optional, Any, Union
import dns.resolver
import whois
from config import Config

# Configure logging
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format=Config.LOG_FORMAT,
    handlers=[
        logging.FileHandler(Config.LOG_FILE),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class OSINTAnalyzer:
    """
    OSINT Security Analysis Framework

    Performs comprehensive OSINT analysis including DNS reconnaissance,
    SSL/TLS analysis, subdomain enumeration, and security assessment.
    """

    def __init__(self, domain: str, authorized: bool = False):
        """
        Initialize OSINT analyzer

        Args:
            domain: Target domain to analyze
            authorized: Whether explicit authorization has been obtained
        """
        self.domain = self._clean_domain(domain)
        self.authorized = authorized
        self.logger = logger
        self.session = self._create_session()

        self.results = {
            'domain': self.domain,
            'timestamp': datetime.datetime.now().isoformat(),
            'reconnaissance': {},
            'osint': {},
            'vulnerabilities': {},
            'recommendations': [],
            'ethical_notice': Config.ETHICAL_NOTICE
        }

        if not self.authorized:
            self._show_ethical_warning()

    def _clean_domain(self, domain: str) -> str:
        """Clean and validate domain input"""
        domain = domain.replace('https://', '').replace('http://', '')
        domain = domain.replace('www.', '').strip('/')
        return domain.lower()

    def _create_session(self) -> requests.Session:
        """Create configured requests session"""
        session = requests.Session()
        session.headers.update(Config.get_headers())
        if Config.get_proxies():
            session.proxies.update(Config.get_proxies())
        return session

    def _show_ethical_warning(self):
        """Display ethical usage warning"""
        print("⚠️  AVISO ÉTICO: Asegúrese de tener autorización explícita "
              "antes de realizar cualquier análisis.")
        print("⚠️  Este framework solo debe usarse para fines educativos "
              "o con autorización del propietario.")

    def check_authorization(self):
        """Verify authorization before proceeding"""
        if not self.authorized:
            response = input(f"¿Tiene autorización explícita para analizar "
                           f"{self.domain}? (si/no): ")
            if response.lower() not in ['si', 'sí', 'yes', 'y']:
                print("❌ Análisis cancelado. Solo proceda con autorización "
                      "explícita.")
                sys.exit(1)
            self.authorized = True
    
    def basic_domain_info(self):
        """Fase 1.1: Información básica de dominio usando información pública"""
        print("\n🔍 Fase 1.1: Recolectando información básica de dominio...")
        
        # Información DNS mejorada
        try:
            ip = socket.gethostbyname(self.domain)
            
            # Información DNS técnica avanzada
            dns_info = {
                'ip_address': ip,
                'a_records': [],
                'aaaa_records': [],  # IPv6
                'mx_records': [],
                'txt_records': [],
                'ns_records': [],
                'cname_records': [],
                'soa_record': None,
                'srv_records': [],
                'ptr_records': [],
                'caa_records': [],  # Certificate Authority Authorization
                'dmarc_record': None,
                'spf_record': None,
                'dkim_records': [],
                'dns_security': {
                    'dnssec_enabled': False,
                    'dns_over_https': False,
                    'dns_over_tls': False
                },
                'reverse_dns': None,
                'dns_propagation': {},
                'dns_performance': {}
            }
            
            resolver = dns.resolver.Resolver()
            
            # Registros A
            try:
                for rdata in resolver.resolve(self.domain, 'A'):
                    dns_info['a_records'].append(str(rdata))
            except:
                pass
            
            # Registros MX
            try:
                for rdata in resolver.resolve(self.domain, 'MX'):
                    dns_info['mx_records'].append(f"{rdata.preference} {rdata.exchange}")
            except:
                pass
            
            # Registros TXT
            try:
                for rdata in resolver.resolve(self.domain, 'TXT'):
                    dns_info['txt_records'].append(str(rdata))
            except:
                pass
            
            # Registros NS
            try:
                for rdata in resolver.resolve(self.domain, 'NS'):
                    dns_info['ns_records'].append(str(rdata))
            except:
                pass
            
            # Registro SOA
            try:
                soa = resolver.resolve(self.domain, 'SOA')[0]
                dns_info['soa_record'] = {
                    'mname': str(soa.mname),
                    'rname': str(soa.rname),
                    'serial': soa.serial,
                    'refresh': soa.refresh,
                    'retry': soa.retry,
                    'expire': soa.expire,
                    'minimum': soa.minimum
                }
            except:
                pass

            # Registros AAAA (IPv6)
            try:
                for rdata in resolver.resolve(self.domain, 'AAAA'):
                    dns_info['aaaa_records'].append(str(rdata))
            except:
                pass

            # Registros CAA (Certificate Authority Authorization)
            try:
                for rdata in resolver.resolve(self.domain, 'CAA'):
                    dns_info['caa_records'].append(str(rdata))
            except:
                pass

            # Análisis de registros de seguridad de email
            # SPF Record
            for txt_record in dns_info['txt_records']:
                if 'v=spf1' in txt_record.lower():
                    dns_info['spf_record'] = txt_record
                    break

            # DMARC Record
            try:
                for rdata in resolver.resolve(f'_dmarc.{self.domain}', 'TXT'):
                    record_text = str(rdata)
                    if 'v=DMARC1' in record_text:
                        dns_info['dmarc_record'] = record_text
                        break
            except:
                pass

            # DKIM Records (common selectors)
            dkim_selectors = ['default', 'google', 'k1', 'selector1', 'selector2']
            for selector in dkim_selectors:
                try:
                    for rdata in resolver.resolve(f'{selector}._domainkey.{self.domain}', 'TXT'):
                        record_text = str(rdata)
                        if 'v=DKIM1' in record_text:
                            dns_info['dkim_records'].append({
                                'selector': selector,
                                'record': record_text
                            })
                except:
                    pass

            # Reverse DNS
            try:
                reverse_name = dns.reversename.from_address(ip)
                reverse_dns = str(resolver.resolve(reverse_name, 'PTR')[0])
                dns_info['reverse_dns'] = reverse_dns
            except:
                pass

            # Análisis de seguridad DNS
            dns_info['dns_security'] = self._analyze_dns_security(dns_info)
            
            self.results['reconnaissance']['dns_info'] = dns_info
            print(f"✓ IP Address: {ip}")
            print(f"✓ Registros DNS: A({len(dns_info['a_records'])}), MX({len(dns_info['mx_records'])}), TXT({len(dns_info['txt_records'])})")
            
        except Exception as e:
            print(f"❌ Error obteniendo información DNS: {e}")
        
        # Información WHOIS
        try:
            w = whois.whois(self.domain)
            whois_info = {
                'registrar': w.registrar,
                'creation_date': str(w.creation_date) if w.creation_date else None,
                'expiration_date': str(w.expiration_date) if w.expiration_date else None,
                'name_servers': w.name_servers if w.name_servers else [],
                'status': w.status if w.status else [],
                'country': w.country,
                'organization': w.org
            }
            self.results['reconnaissance']['whois'] = whois_info
            print(f"✓ Registrar: {whois_info['registrar']}")
            if whois_info['creation_date']:
                print(f"✓ Fecha de creación: {whois_info['creation_date'][:10]}")
        except Exception as e:
            print(f"⚠️  No se pudo obtener información WHOIS: {e}")
        
        # Geolocalización básica de IP
        try:
            geo_response = requests.get(f"http://ipapi.co/{ip}/json/", timeout=5)
            if geo_response.status_code == 200:
                geo_data = geo_response.json()
                geo_info = {
                    'country': geo_data.get('country_name'),
                    'region': geo_data.get('region'),
                    'city': geo_data.get('city'),
                    'isp': geo_data.get('org'),
                    'timezone': geo_data.get('timezone')
                }
                self.results['reconnaissance']['geolocation'] = geo_info
                print(f"✓ Ubicación: {geo_info['city']}, {geo_info['country']}")
                print(f"✓ ISP: {geo_info['isp']}")
        except:
            pass
    
    def ssl_analysis(self):
        """Fase 1.2: Análisis SSL/TLS técnico avanzado"""
        print("\n🔒 Fase 1.2: Analizando configuración SSL/TLS...")

        ssl_info = {}
        try:
            context = ssl.create_default_context()
            with socket.create_connection((self.domain, 443), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=self.domain) as ssock:
                    cert = ssock.getpeercert()
                    cert_der = ssock.getpeercert(binary_form=True)

                    # Información básica del certificado
                    ssl_info = {
                        'ssl_version': ssock.version(),
                        'cipher_suite': str(ssock.cipher()[0]) if ssock.cipher() else None,
                        'cipher_bits': ssock.cipher()[2] if ssock.cipher() and len(ssock.cipher()) > 2 else None,
                        'cipher_version': ssock.cipher()[1] if ssock.cipher() and len(ssock.cipher()) > 1 else None,
                        'issuer': dict(x[0] for x in cert['issuer']),
                        'subject': dict(x[0] for x in cert['subject']),
                        'expiry_date': cert['notAfter'],
                        'start_date': cert['notBefore'],
                        'san': [],
                        'serial_number': cert.get('serialNumber'),
                        'signature_algorithm': cert.get('signatureAlgorithm'),
                        'version': cert.get('version'),
                        'public_key_algorithm': None,
                        'public_key_size': None,
                        'fingerprint_sha1': None,
                        'fingerprint_sha256': None,
                        'extensions': {},
                        'certificate_chain_length': 0,
                        'ocsp_stapling': False,
                        'sct_extension': False
                    }

                    # Subject Alternative Names
                    for ext in cert.get('subjectAltName', []):
                        if ext[0] == 'DNS':
                            ssl_info['san'].append(ext[1])
                        elif ext[0] == 'IP Address':
                            ssl_info['san'].append(f"IP:{ext[1]}")

                    # Análisis de extensiones del certificado
                    if cert_der:
                        try:
                            from cryptography import x509
                            from cryptography.hazmat.backends import default_backend
                            import hashlib

                            cert_obj = x509.load_der_x509_certificate(cert_der, default_backend())

                            # Información de la clave pública
                            public_key = cert_obj.public_key()
                            ssl_info['public_key_algorithm'] = public_key.__class__.__name__.replace('_', ' ').replace('PublicKey', '')
                            ssl_info['public_key_size'] = public_key.key_size

                            # Fingerprints
                            ssl_info['fingerprint_sha1'] = hashlib.sha1(cert_der).hexdigest().upper()
                            ssl_info['fingerprint_sha256'] = hashlib.sha256(cert_der).hexdigest().upper()

                            # Extensiones del certificado
                            for ext in cert_obj.extensions:
                                ext_name = ext.oid._name
                                ssl_info['extensions'][ext_name] = {
                                    'critical': ext.critical,
                                    'oid': str(ext.oid)
                                }

                                # Verificar OCSP Stapling
                                if 'authorityInfoAccess' in ext_name:
                                    ssl_info['ocsp_stapling'] = True

                                # Verificar SCT (Certificate Transparency)
                                if 'signedCertificateTimestamps' in ext_name:
                                    ssl_info['sct_extension'] = True

                        except ImportError:
                            print("⚠️  cryptography library no disponible para análisis avanzado")
                        except Exception as e:
                            print(f"⚠️  Error en análisis avanzado de certificado: {e}")

                    # Análisis de la cadena de certificados
                    try:
                        peer_cert_chain = ssock.getpeercert_chain()
                        if peer_cert_chain:
                            ssl_info['certificate_chain_length'] = len(peer_cert_chain)
                    except:
                        pass

                    # Evaluación de seguridad del cipher
                    cipher_security = self._evaluate_cipher_security(ssl_info['cipher_suite'])
                    ssl_info['cipher_security'] = cipher_security

                    print(f"✓ SSL/TLS Version: {ssl_info['ssl_version']}")
                    print(f"✓ Cipher Suite: {ssl_info['cipher_suite']} ({ssl_info['cipher_bits']} bits)")
                    print(f"✓ Cipher Security: {cipher_security}")
                    print(f"✓ Public Key: {ssl_info['public_key_algorithm']} {ssl_info['public_key_size']} bits")
                    print(f"✓ Emisor: {ssl_info['issuer'].get('organizationName', 'Desconocido')}")
                    print(f"✓ Válido: {ssl_info['start_date']} - {ssl_info['expiry_date']}")
                    print(f"✓ SANs: {len(ssl_info['san'])} dominios")
                    print(f"✓ Extensiones: {len(ssl_info['extensions'])}")
                    print(f"✓ OCSP Stapling: {'Sí' if ssl_info['ocsp_stapling'] else 'No'}")
                    print(f"✓ Certificate Transparency: {'Sí' if ssl_info['sct_extension'] else 'No'}")

        except Exception as e:
            print(f"❌ Error en análisis SSL: {e}")

        self.results['reconnaissance']['ssl_info'] = ssl_info
        return ssl_info

    def _evaluate_cipher_security(self, cipher_suite):
        """Evaluar la seguridad del cipher suite"""
        if not cipher_suite:
            return "Desconocido"

        cipher_lower = cipher_suite.lower()

        # Ciphers inseguros
        if any(weak in cipher_lower for weak in ['rc4', 'des', 'md5', 'sha1', 'null']):
            return "Inseguro"

        # Ciphers obsoletos
        if any(obsolete in cipher_lower for obsolete in ['3des', 'cbc']):
            return "Obsoleto"

        # Ciphers seguros modernos
        if any(secure in cipher_lower for secure in ['aes256-gcm', 'chacha20', 'aes128-gcm']):
            return "Seguro"

        # Ciphers aceptables
        if 'aes' in cipher_lower:
            return "Aceptable"

        return "Revisar"

    def _analyze_dns_security(self, dns_info):
        """Analizar configuración de seguridad DNS"""
        security_analysis = {
            'email_security_score': 0,
            'dns_security_score': 0,
            'recommendations': [],
            'findings': []
        }

        # Análisis de seguridad de email
        if dns_info.get('spf_record'):
            security_analysis['email_security_score'] += 30
            security_analysis['findings'].append('SPF configurado')
        else:
            security_analysis['recommendations'].append('Configurar registro SPF')

        if dns_info.get('dmarc_record'):
            security_analysis['email_security_score'] += 40
            security_analysis['findings'].append('DMARC configurado')
        else:
            security_analysis['recommendations'].append('Configurar registro DMARC')

        if dns_info.get('dkim_records'):
            security_analysis['email_security_score'] += 30
            security_analysis['findings'].append(f'DKIM configurado ({len(dns_info["dkim_records"])} selectores)')
        else:
            security_analysis['recommendations'].append('Configurar registros DKIM')

        # Análisis de CAA
        if dns_info.get('caa_records'):
            security_analysis['dns_security_score'] += 25
            security_analysis['findings'].append('CAA configurado')
        else:
            security_analysis['recommendations'].append('Configurar registros CAA')

        # Análisis de IPv6
        if dns_info.get('aaaa_records'):
            security_analysis['dns_security_score'] += 15
            security_analysis['findings'].append('IPv6 configurado')

        # Reverse DNS
        if dns_info.get('reverse_dns'):
            security_analysis['dns_security_score'] += 10
            security_analysis['findings'].append('Reverse DNS configurado')

        # Análisis de subdominios en certificados
        san_count = len(dns_info.get('san', []))
        if san_count > 0:
            security_analysis['dns_security_score'] += min(20, san_count * 2)
            security_analysis['findings'].append(f'SAN configurado ({san_count} dominios)')

        # Puntuación final
        total_score = (security_analysis['email_security_score'] +
                      security_analysis['dns_security_score']) / 2

        if total_score >= 80:
            security_analysis['level'] = 'Excelente'
        elif total_score >= 60:
            security_analysis['level'] = 'Bueno'
        elif total_score >= 40:
            security_analysis['level'] = 'Regular'
        else:
            security_analysis['level'] = 'Deficiente'

        security_analysis['total_score'] = total_score

        return security_analysis

    def robots_sitemap_analysis(self):
        """Fase 1.3: Análisis de robots.txt y sitemap.xml mejorado"""
        print("\n🤖 Fase 1.3: Analizando robots.txt y sitemap.xml...")
        
        web_info = {}
        
        # Analizar robots.txt
        try:
            response = requests.get(f"https://{self.domain}/robots.txt", timeout=10)
            if response.status_code == 200:
                robots_content = response.text
                web_info['robots_txt'] = robots_content
                
                # Extraer información útil de robots.txt
                disallow_paths = re.findall(r'Disallow:\s*(.+)', robots_content)
                sitemap_urls = re.findall(r'Sitemap:\s*(.+)', robots_content)
                
                web_info['robots_analysis'] = {
                    'disallow_paths': disallow_paths[:10],  # Primeros 10
                    'sitemap_urls': sitemap_urls,
                    'user_agents': re.findall(r'User-agent:\s*(.+)', robots_content)
                }
                
                print(f"✓ robots.txt encontrado ({len(disallow_paths)} rutas bloqueadas)")
                if sitemap_urls:
                    print(f"✓ {len(sitemap_urls)} sitemaps referenciados")
            else:
                print("⚠️  robots.txt no encontrado")
        except Exception as e:
            print(f"❌ Error accediendo a robots.txt: {e}")
        
        # Analizar sitemap.xml
        try:
            response = requests.get(f"https://{self.domain}/sitemap.xml", timeout=10)
            if response.status_code == 200:
                sitemap_content = response.text
                
                # Extraer URLs del sitemap
                urls = re.findall(r'<loc>(.*?)</loc>', sitemap_content)
                web_info['sitemap_xml'] = sitemap_content[:2000]  # Primeros 2000 caracteres
                web_info['sitemap_urls'] = urls[:20]  # Primeras 20 URLs
                
                print(f"✓ sitemap.xml encontrado ({len(urls)} URLs)")
            else:
                print("⚠️  sitemap.xml no encontrado")
        except Exception as e:
            print(f"❌ Error accediendo a sitemap.xml: {e}")
        
        # Buscar archivos comunes adicionales
        common_files = [
            'security.txt', '.well-known/security.txt',
            'humans.txt', 'ads.txt', 'app-ads.txt'
        ]
        
        found_files = {}
        for file_path in common_files:
            try:
                response = requests.get(f"https://{self.domain}/{file_path}", timeout=5)
                if response.status_code == 200:
                    found_files[file_path] = response.text[:500]  # Primeros 500 caracteres
                    print(f"✓ {file_path} encontrado")
            except:
                pass
        
        if found_files:
            web_info['additional_files'] = found_files
        
        self.results['reconnaissance']['web_info'] = web_info
        return web_info
    
    def subdomain_enumeration(self):
        """Fase 2.2: Enumeración de subdominios mejorada"""
        print("\n🌐 Fase 2.2: Enumerando subdominios públicos...")
        
        subdomains = set()
        subdomain_sources = {}
        
        # Certificate Transparency
        try:
            print("🔍 Buscando en Certificate Transparency...")
            url = f"https://crt.sh/?q=%.{self.domain}&output=json"
            response = requests.get(url, timeout=15)
            if response.status_code == 200:
                data = response.json()
                ct_subdomains = set()
                for entry in data:
                    name_value = entry.get('name_value', '')
                    for subdomain in name_value.split('\n'):
                        clean_subdomain = subdomain.strip()
                        if clean_subdomain.endswith(self.domain) and clean_subdomain != self.domain:
                            ct_subdomains.add(clean_subdomain)
                            subdomains.add(clean_subdomain)
                
                subdomain_sources['certificate_transparency'] = list(ct_subdomains)
                print(f"✓ Certificate Transparency: {len(ct_subdomains)} subdominios")
        except Exception as e:
            print(f"❌ Error en Certificate Transparency: {e}")
        
        # CommonCrawl
        try:
            print("🔍 Buscando en CommonCrawl...")
            cc_url = f"http://index.commoncrawl.org/CC-MAIN-2024-10-index?url=*.{self.domain}&output=json"
            response = requests.get(cc_url, timeout=10)
            if response.status_code == 200:
                cc_subdomains = set()
                for line in response.text.strip().split('\n')[:100]:  # Limitar a 100 resultados
                    try:
                        data = json.loads(line)
                        url = data.get('url', '')
                        domain_match = re.search(r'https?://([^/]+)', url)
                        if domain_match:
                            found_domain = domain_match.group(1)
                            if found_domain.endswith(self.domain) and found_domain != self.domain:
                                cc_subdomains.add(found_domain)
                                subdomains.add(found_domain)
                    except:
                        continue
                
                subdomain_sources['commoncrawl'] = list(cc_subdomains)
                print(f"✓ CommonCrawl: {len(cc_subdomains)} subdominios")
        except Exception as e:
            print(f"⚠️  CommonCrawl no disponible: {e}")
        
        # Búsqueda DNS con wordlist básica
        try:
            print("🔍 Probando subdominios comunes...")
            common_subdomains = [
                'www', 'mail', 'ftp', 'admin', 'test', 'dev', 'staging', 'api',
                'blog', 'shop', 'support', 'help', 'docs', 'cdn', 'static',
                'img', 'images', 'app', 'mobile', 'secure', 'vpn', 'remote'
            ]
            
            dns_subdomains = set()
            for sub in common_subdomains:
                try:
                    full_domain = f"{sub}.{self.domain}"
                    socket.gethostbyname(full_domain)
                    dns_subdomains.add(full_domain)
                    subdomains.add(full_domain)
                except:
                    pass
            
            subdomain_sources['dns_bruteforce'] = list(dns_subdomains)
            print(f"✓ DNS Bruteforce: {len(dns_subdomains)} subdominios")
        except Exception as e:
            print(f"❌ Error en DNS bruteforce: {e}")
        
        self.results['osint']['subdomains'] = list(subdomains)
        self.results['osint']['subdomain_sources'] = subdomain_sources
        print(f"📊 Total de subdominios únicos encontrados: {len(subdomains)}")
        
        return list(subdomains)
    
    def port_scanning_advanced(self):
        """Escaneo avanzado de puertos con detección de servicios"""
        print("\n🔌 Fase 2.3: Escaneando puertos y detectando servicios...")

        # Puertos comunes categorizados
        port_categories = {
            'web': [80, 443, 8080, 8443, 8000, 8888, 9000, 3000],
            'mail': [25, 110, 143, 465, 587, 993, 995],
            'ftp': [20, 21, 22, 990],
            'dns': [53],
            'database': [1433, 1521, 3306, 5432, 6379, 27017],
            'remote': [22, 23, 3389, 5900, 5901],
            'other': [135, 139, 445, 161, 162, 389, 636, 1723, 2049]
        }

        all_ports = []
        for ports in port_categories.values():
            all_ports.extend(ports)

        open_ports = []
        service_info = {}

        def scan_port_advanced(port):
            try:
                with socket.create_connection((self.domain, port), timeout=3) as sock:
                    # Intentar detectar el servicio
                    service = self._detect_service(sock, port)
                    return port, service
            except:
                return None, None

        print(f"🔍 Escaneando {len(all_ports)} puertos...")

        with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
            future_to_port = {executor.submit(scan_port_advanced, port): port
                             for port in all_ports}

            for future in concurrent.futures.as_completed(future_to_port):
                port, service = future.result()
                if port:
                    open_ports.append(port)
                    if service:
                        service_info[port] = service

        # Categorizar puertos encontrados
        categorized_ports = {}
        for category, ports in port_categories.items():
            found_ports = [p for p in open_ports if p in ports]
            if found_ports:
                categorized_ports[category] = found_ports

        # Análisis de seguridad de puertos
        security_analysis = self._analyze_port_security(open_ports, service_info)

        port_results = {
            'open_ports': sorted(open_ports),
            'categorized_ports': categorized_ports,
            'service_detection': service_info,
            'security_analysis': security_analysis,
            'total_open': len(open_ports)
        }

        if open_ports:
            print(f"✓ {len(open_ports)} puertos abiertos encontrados:")
            for category, ports in categorized_ports.items():
                print(f"   📂 {category.title()}: {', '.join(map(str, ports))}")

            # Mostrar servicios detectados
            if service_info:
                print(f"✓ Servicios detectados:")
                for port, service in service_info.items():
                    print(f"   🔍 Puerto {port}: {service}")
        else:
            print("ℹ️  No se encontraron puertos abiertos")

        self.results['reconnaissance']['port_analysis'] = port_results
        return port_results

    def _detect_service(self, sock, port):
        """Detectar servicio en puerto específico"""
        try:
            # Enviar datos de prueba según el puerto
            if port == 80:
                sock.send(b"GET / HTTP/1.0\r\n\r\n")
                response = sock.recv(1024).decode('utf-8', errors='ignore')
                if 'HTTP' in response:
                    server = self._extract_server_header(response)
                    return f"HTTP{' - ' + server if server else ''}"

            elif port == 443:
                return "HTTPS/SSL"

            elif port == 22:
                response = sock.recv(1024).decode('utf-8', errors='ignore')
                if 'SSH' in response:
                    return f"SSH - {response.strip()}"

            elif port == 21:
                response = sock.recv(1024).decode('utf-8', errors='ignore')
                if 'FTP' in response:
                    return f"FTP - {response.strip()}"

            elif port == 25:
                response = sock.recv(1024).decode('utf-8', errors='ignore')
                if 'SMTP' in response or '220' in response:
                    return f"SMTP - {response.strip()}"

            elif port == 53:
                return "DNS"

            elif port in [3306, 1433, 5432, 1521]:
                db_names = {3306: 'MySQL', 1433: 'MSSQL', 5432: 'PostgreSQL', 1521: 'Oracle'}
                return f"Database - {db_names[port]}"

            else:
                # Intento genérico
                sock.settimeout(2)
                response = sock.recv(512).decode('utf-8', errors='ignore')
                if response:
                    return f"Unknown - {response[:50].strip()}"
                else:
                    return "Unknown Service"

        except:
            return "Unknown Service"

    def _extract_server_header(self, response):
        """Extraer información del servidor de la respuesta HTTP"""
        lines = response.split('\n')
        for line in lines:
            if line.lower().startswith('server:'):
                return line.split(':', 1)[1].strip()
        return None

    def _analyze_port_security(self, open_ports, service_info):
        """Analizar seguridad de puertos abiertos"""
        analysis = {
            'risk_level': 'Bajo',
            'concerns': [],
            'recommendations': []
        }

        # Puertos de alto riesgo
        high_risk_ports = [21, 23, 135, 139, 445, 1433, 3306, 5432]
        risky_ports = [p for p in open_ports if p in high_risk_ports]

        if risky_ports:
            analysis['risk_level'] = 'Alto'
            analysis['concerns'].append(f"Puertos de alto riesgo abiertos: {risky_ports}")
            analysis['recommendations'].append("Cerrar puertos innecesarios o restringir acceso")

        # Servicios de base de datos expuestos
        db_ports = [p for p in open_ports if p in [1433, 3306, 5432, 1521, 6379, 27017]]
        if db_ports:
            analysis['concerns'].append(f"Bases de datos potencialmente expuestas: {db_ports}")
            analysis['recommendations'].append("Restringir acceso a bases de datos solo a IPs autorizadas")

        # Demasiados puertos abiertos
        if len(open_ports) > 10:
            analysis['risk_level'] = 'Medio' if analysis['risk_level'] == 'Bajo' else analysis['risk_level']
            analysis['concerns'].append(f"Muchos puertos abiertos ({len(open_ports)})")
            analysis['recommendations'].append("Revisar necesidad de todos los servicios expuestos")

        return analysis
    
    def security_headers_analysis(self):
        """Fase 3.3: Análisis de cabeceras de seguridad mejorado"""
        print("\n🛡️  Fase 3.3: Analizando cabeceras de seguridad...")
        
        security_headers = {}
        response_info = {}
        
        try:
            response = requests.get(f"https://{self.domain}", timeout=10, allow_redirects=True)
            headers = response.headers
            
            # Información adicional de la respuesta
            response_info = {
                'status_code': response.status_code,
                'content_type': headers.get('Content-Type', ''),
                'content_length': headers.get('Content-Length', ''),
                'server': headers.get('Server', ''),
                'date': headers.get('Date', ''),
                'last_modified': headers.get('Last-Modified', ''),
                'etag': headers.get('ETag', ''),
                'final_url': response.url
            }
            
            security_header_list = [
                'Content-Security-Policy',
                'X-XSS-Protection',
                'X-Frame-Options',
                'Strict-Transport-Security',
                'Referrer-Policy',
                'Permissions-Policy',
                'X-Content-Type-Options',
                'Feature-Policy',
                'Expect-CT',
                'X-Permitted-Cross-Domain-Policies'
            ]
            
            for header in security_header_list:
                if header in headers:
                    security_headers[header] = headers[header]
                    print(f"✓ {header}: Presente")
                else:
                    security_headers[header] = "Ausente"
                    print(f"⚠️  {header}: Ausente")
            
            # Análisis adicional de cookies
            cookies_info = []
            for cookie in response.cookies:
                cookie_info = {
                    'name': cookie.name,
                    'secure': cookie.secure,
                    'httponly': hasattr(cookie, 'has_nonstandard_attr') and cookie.has_nonstandard_attr('HttpOnly'),
                    'samesite': getattr(cookie, 'get_nonstandard_attr', lambda x, default=None: default)('SameSite')
                }
                cookies_info.append(cookie_info)
            
            if cookies_info:
                response_info['cookies'] = cookies_info
                print(f"ℹ️  Encontradas {len(cookies_info)} cookies")
        
        except Exception as e:
            print(f"❌ Error analizando cabeceras: {e}")
        
        self.results['vulnerabilities']['security_headers'] = security_headers
        self.results['reconnaissance']['response_info'] = response_info
        return security_headers
    
    def technology_detection(self):
        """Detectar tecnologías web mejorado"""
        print("\n💻 Detectando tecnologías web...")
        
        technologies = {}
        try:
            response = requests.get(f"https://{self.domain}", timeout=10)
            headers = response.headers
            content = response.text[:10000]  # Primeros 10000 caracteres
            
            # Detectar servidor web
            if 'Server' in headers:
                technologies['web_server'] = headers['Server']
                print(f"✓ Servidor Web: {technologies['web_server']}")
            
            # Detectar tecnologías por headers
            header_techs = {}
            tech_headers = {
                'X-Powered-By': 'framework',
                'X-Generator': 'cms',
                'X-Drupal-Cache': 'Drupal',
                'X-Pingback': 'WordPress'
            }
            
            for header, tech in tech_headers.items():
                if header in headers:
                    header_techs[header] = headers[header]
                    print(f"✓ {header}: {headers[header]}")
            
            # Detectar tecnologías comunes en contenido
            tech_indicators = {
                'WordPress': ['wp-content', 'wp-includes', 'wp-json'],
                'Drupal': ['drupal', 'sites/default', '/core/'],
                'Joomla': ['joomla', 'com_content', 'option=com_'],
                'React': ['react', 'ReactDOM', '__REACT'],
                'Angular': ['angular', 'ng-', 'AngularJS'],
                'Vue.js': ['vue.js', '__vue__', 'Vue.'],
                'jQuery': ['jquery', '$.fn.jquery', 'jQuery'],
                'Bootstrap': ['bootstrap', 'btn-', 'container-'],
                'Shopify': ['shopify', 'myshopify'],
                'Magento': ['magento', 'mage/'],
                'Laravel': ['laravel', 'laravel_session'],
                'Django': ['django', 'csrfmiddlewaretoken'],
                'Flask': ['flask', 'Werkzeug'],
                'Express': ['express', 'X-Powered-By: Express'],
                'Next.js': ['next.js', '_next/', '__NEXT_DATA__'],
                'Gatsby': ['gatsby', 'gatsby-'],
                'Cloudflare': ['cloudflare', '__cf_bm', 'cf-ray']
            }
            
            detected_techs = []
            for tech, indicators in tech_indicators.items():
                if any(indicator.lower() in content.lower() or indicator.lower() in str(headers).lower() for indicator in indicators):
                    detected_techs.append(tech)
            
            technologies['detected'] = detected_techs
            technologies['header_technologies'] = header_techs
            
            # JavaScript frameworks específicos
            js_frameworks = []
            js_patterns = {
                'React': r'react[.-](\d+\.\d+\.\d+)',
                'Angular': r'angular[.-](\d+\.\d+\.\d+)',
                'Vue': r'vue[.-](\d+\.\d+\.\d+)',
                'jQuery': r'jquery[.-](\d+\.\d+\.\d+)'
            }
            
            for framework, pattern in js_patterns.items():
                matches = re.findall(pattern, content.lower())
                if matches:
                    js_frameworks.append(f"{framework} {matches[0]}")
            
            if js_frameworks:
                technologies['javascript_frameworks'] = js_frameworks
            
            print(f"✓ Tecnologías detectadas: {', '.join(detected_techs) if detected_techs else 'Ninguna específica'}")
            if js_frameworks:
                print(f"✓ Frameworks JS: {', '.join(js_frameworks)}")
            
        except Exception as e:
            print(f"❌ Error detectando tecnologías: {e}")
        
        self.results['reconnaissance']['technology'] = technologies
        return technologies
    
    def generate_recommendations(self):
        """Generar recomendaciones basadas en hallazgos"""
        recommendations = []

        # Recomendaciones basadas en cabeceras de seguridad
        security_headers = self.results.get('vulnerabilities', {}).get('security_headers', {})

        # Cabeceras críticas de seguridad
        critical_headers = {
            'Content-Security-Policy': {
                'issue': 'Content-Security-Policy ausente',
                'recommendation': 'Implementar CSP para prevenir ataques XSS y de inyección de código. Ejemplo: "Content-Security-Policy: default-src \'self\'; script-src \'self\' \'unsafe-inline\'"',
                'priority': 'Alta',
                'impact': 'Previene ataques XSS, clickjacking y inyección de código malicioso'
            },
            'Strict-Transport-Security': {
                'issue': 'HSTS (HTTP Strict Transport Security) ausente',
                'recommendation': 'Implementar HSTS para forzar conexiones HTTPS. Ejemplo: "Strict-Transport-Security: max-age=31536000; includeSubDomains"',
                'priority': 'Alta',
                'impact': 'Previene ataques de downgrade y man-in-the-middle'
            },
            'X-Frame-Options': {
                'issue': 'X-Frame-Options ausente',
                'recommendation': 'Implementar X-Frame-Options para prevenir clickjacking. Usar "DENY" o "SAMEORIGIN"',
                'priority': 'Media',
                'impact': 'Previene ataques de clickjacking y embedding malicioso'
            },
            'X-Content-Type-Options': {
                'issue': 'X-Content-Type-Options ausente',
                'recommendation': 'Implementar "X-Content-Type-Options: nosniff" para prevenir MIME type sniffing',
                'priority': 'Media',
                'impact': 'Previene ataques basados en interpretación incorrecta de tipos MIME'
            },
            'Referrer-Policy': {
                'issue': 'Referrer-Policy ausente',
                'recommendation': 'Implementar política de referrer para controlar información enviada. Ejemplo: "Referrer-Policy: strict-origin-when-cross-origin"',
                'priority': 'Baja',
                'impact': 'Controla qué información de referrer se envía en las peticiones'
            },
            'X-XSS-Protection': {
                'issue': 'X-XSS-Protection ausente',
                'recommendation': 'Implementar "X-XSS-Protection: 1; mode=block" para activar filtro XSS del navegador',
                'priority': 'Media',
                'impact': 'Activa protección XSS integrada del navegador'
            }
        }

        for header, config in critical_headers.items():
            if security_headers.get(header) == 'Ausente':
                recommendations.append({
                    'category': 'Cabeceras de Seguridad',
                    'issue': config['issue'],
                    'recommendation': config['recommendation'],
                    'priority': config['priority'],
                    'impact': config['impact']
                })

        # Recomendaciones basadas en SSL/TLS
        ssl_info = self.results.get('reconnaissance', {}).get('ssl_info', {})
        if ssl_info:
            expiry_date = ssl_info.get('expiry_date', '')
            if expiry_date:
                try:
                    # Parsear fecha de expiración
                    from datetime import datetime, timedelta
                    expiry = datetime.strptime(expiry_date.replace(' GMT', ''), '%b %d %H:%M:%S %Y')
                    days_until_expiry = (expiry - datetime.now()).days

                    if days_until_expiry < 30:
                        recommendations.append({
                            'category': 'Certificados SSL',
                            'issue': f'Certificado SSL expira en {days_until_expiry} días',
                            'recommendation': 'Renovar certificado SSL antes de su expiración para evitar interrupciones del servicio',
                            'priority': 'Alta' if days_until_expiry < 7 else 'Media',
                            'impact': 'Evita interrupciones del servicio y advertencias de seguridad'
                        })
                except:
                    pass

        # Recomendaciones basadas en subdominios
        subdomains = self.results.get('osint', {}).get('subdomains', [])
        if len(subdomains) > 10:
            recommendations.append({
                'category': 'Gestión de Subdominios',
                'issue': f'Se encontraron {len(subdomains)} subdominios',
                'recommendation': 'Revisar y auditar todos los subdominios para asegurar que estén autorizados y seguros. Implementar wildcard SSL si es necesario.',
                'priority': 'Media',
                'impact': 'Reduce superficie de ataque y mejora gestión de seguridad'
            })

        # Recomendaciones basadas en puertos abiertos
        open_ports = self.results.get('reconnaissance', {}).get('open_ports', [])
        if open_ports:
            non_standard_ports = [p for p in open_ports if p not in [80, 443]]
            if non_standard_ports:
                recommendations.append({
                    'category': 'Configuración de Red',
                    'issue': f'Puertos no estándar abiertos: {", ".join(map(str, non_standard_ports))}',
                    'recommendation': 'Revisar la necesidad de mantener abiertos estos puertos y implementar firewall si es necesario',
                    'priority': 'Media',
                    'impact': 'Reduce superficie de ataque y mejora seguridad perimetral'
                })

        # Recomendaciones generales de monitoreo
        recommendations.append({
            'category': 'Monitoreo y Alertas',
            'issue': 'Monitoreo continuo de seguridad',
            'recommendation': 'Implementar monitoreo continuo de certificados SSL, cambios DNS, nuevos subdominios y alertas de amenazas',
            'priority': 'Alta',
            'impact': 'Detección temprana de amenazas y cambios no autorizados'
        })

        # Recomendación de auditoría regular
        recommendations.append({
            'category': 'Auditoría de Seguridad',
            'issue': 'Auditorías de seguridad regulares',
            'recommendation': 'Realizar auditorías de seguridad regulares (mensual/trimestral) para identificar nuevas vulnerabilidades',
            'priority': 'Media',
            'impact': 'Mantiene postura de seguridad actualizada y efectiva'
        })

        self.results['recommendations'] = recommendations
        return recommendations
    
    def generate_report(self):
        """Generar informe ejecutivo"""
        print("\n📊 Generando informe ejecutivo...")
        
        report = {
            'executive_summary': {
                'domain': self.domain,
                'analysis_date': self.results['timestamp'],
                'total_findings': len(self.results.get('recommendations', [])),
                'risk_level': self._calculate_risk_level()
            },
            'detailed_findings': self.results,
            'recommendations': self.results.get('recommendations', [])
        }
        
        # Guardar informe en JSON
        filename = f"osint_report_{self.domain}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Guardar el nombre del archivo como atributo para acceso posterior
        self.report_filename = filename
        
        print(f"✓ Informe guardado en: {filename}")
        
        # Generar informe HTML también
        try:
            from report_generator import ReportGenerator
            generator = ReportGenerator(filename)
            html_filename = generator.generate_html_report()
            print(f"✅ Informe HTML generado: {html_filename}")
        except Exception as e:
            print(f"⚠️  Error generando HTML: {e}")
        
        return report
    
    def _calculate_risk_level(self):
        """Calcular nivel de riesgo basado en hallazgos"""
        security_headers = self.results.get('vulnerabilities', {}).get('security_headers', {})
        missing_headers = sum(1 for v in security_headers.values() if v == 'Ausente')
        
        if missing_headers >= 5:
            return 'Alto'
        elif missing_headers >= 3:
            return 'Medio'
        else:
            return 'Bajo'
    
    def run_full_analysis(self):
        """Ejecutar análisis completo"""
        print(f"\n🚀 Iniciando análisis OSINT para: {self.domain}")
        print("=" * 60)
        
        # Verificar autorización
        self.check_authorization()
        
        # Ejecutar todas las fases
        try:
            self.basic_domain_info()
            self.ssl_analysis()
            self.robots_sitemap_analysis()
            self.subdomain_enumeration()
            self.port_scanning_advanced()
            self.security_headers_analysis()
            self.technology_detection()
            self.generate_recommendations()
            
            # Generar informe final
            report = self.generate_report()
            
            print("\n" + "=" * 60)
            print("✅ Análisis completado exitosamente")
            print(f"📊 Nivel de riesgo evaluado: {report['executive_summary']['risk_level']}")
            print(f"📋 Total de recomendaciones: {report['executive_summary']['total_findings']}")
            
            return report
            
        except KeyboardInterrupt:
            print("\n❌ Análisis interrumpido por el usuario")
            sys.exit(1)
        except Exception as e:
            print(f"\n❌ Error durante el análisis: {e}")
            sys.exit(1)

def main():
    parser = argparse.ArgumentParser(description='OSINT Security Analysis Framework')
    parser.add_argument('domain', help='Dominio a analizar')
    parser.add_argument('--authorized', action='store_true', 
                       help='Confirmar que tiene autorización para el análisis')
    
    args = parser.parse_args()
    
    # Limpiar dominio (quitar https:// si está presente)
    domain = args.domain.replace('https://', '').replace('http://', '').replace('www.', '')
    
    analyzer = OSINTAnalyzer(domain, args.authorized)
    analyzer.run_full_analysis()

if __name__ == "__main__":
    main() 