<div align="center">

# 🛡️ CyberScan Toolkit v2.0 🛡️

<p align="center">
  <em>Una plataforma avanzada para pruebas de penetración, inteligencia OSINT y análisis de vulnerabilidades con interfaz ultramoderna</em>
</p>

![Versión](https://img.shields.io/badge/versión-2.0-blue)
![Python](https://img.shields.io/badge/Python-3.7%2B-brightgreen)
![Licencia](https://img.shields.io/badge/Licencia-Privada-red)

</div>

## Características

### Herramientas Avanzadas
- **Integración OSINT**: Whois, NSLookup, DIG, Shodan, theHarvester, SocialScan y más
- **Análisis de Vulnerabilidades**: Nmap, Nikto, SSL scanning, WAF detection, CMS scanning
- **Herramientas de Pentesting**: SQLMap, XSS Hunter, JWT Pentesting, Hydra, NoSQLMap, Responder

### Interfaz Ultramoderna
- **Interfaz Glass-Morphism**: Diseño moderno con efectos de vidrio y transparencia
- **Animaciones Avanzadas**: Efectos de partículas interactivas y visualizaciones 3D
- **Tiempo Real**: Visualización en vivo de resultados de escaneo con efectos visuales
- **Diseño Responsivo**: Optimizado para dispositivos móviles y pantallas grandes

### Visualización y Reportes
- **Visualización 3D**: Mapeo de redes y activos con Three.js
- **Gráficos Interactivos**: Visualización de datos con Chart.js
- **Reportes PDF**: Informes profesionales personalizables con secciones configurables
- **Exportación de Datos**: Formatos JSON, CSV y PDF para análisis posterior

## 📋 Requisitos

- Python 3.7+
- Dependencias listadas en `requirements.txt`
- Herramientas externas (opcionales, pero recomendadas):
  - `whois`, `nslookup`, `dig` - Análisis DNS y dominio
  - `nmap`, `nikto` - Escaneo de vulnerabilidades
  - `sqlmap` - Pruebas de inyección SQL
  - `hydra` - Ataques de fuerza bruta
  - `responder` - Ataques MITM

## 💻 Instalación

### 1. Clonar el repositorio

```bash
git clone https://github.com/stoja88/osintdesk.git
cd osintdesk
```

### 2. Crear entorno virtual (recomendado)

```bash
python -m venv venv

# En Linux/macOS
source venv/bin/activate

# En Windows
venv\Scripts\activate
```

### 3. Instalar dependencias Python

```bash
pip install -r requirements.txt
```

### 4. Instalar herramientas externas

Ejecuta el script de instalación incluido:

```bash
# En Linux/macOS
chmod +x install_all_tools.sh
./install_all_tools.sh

# En Windows, ejecutar install_all_tools.bat
```

#### Instalación manual de herramientas (alternativa)

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install whois dnsutils python3-nmap nikto sqlmap hydra
```

**macOS (con Homebrew):**
```bash
brew install whois bind nmap nikto sqlmap hydra
```

**Windows:**
La mayoría de herramientas necesitan ser instaladas por separado y añadidas al PATH.

### 5. Configurar variables de entorno

Copia el archivo de ejemplo y configura tus claves API:

```bash
cp api_keys.env.example api_keys.env
# Edita api_keys.env con tus claves API
```

## 🚀 Uso

### Iniciar la aplicación

```bash
# Asegúrate de que el entorno virtual está activado
python app.py
```

La aplicación se ejecutará en `http://localhost:5002`

### Principales funcionalidades

1. **Inteligencia OSINT**: Ingresa un dominio o IP en el campo de objetivo y selecciona las herramientas OSINT a utilizar.

2. **Análisis de Vulnerabilidades**: Selecciona las herramientas de vulnerabilidades deseadas y ejecuta el escaneo.

3. **Visualización 3D**: Accede a la visualización 3D desde la interfaz principal para explorar relaciones entre datos.

4. **Generación de Informes**: Al finalizar el escaneo, genera informes en PDF con resultados detallados.

## 📊 Visualizaciones

La aplicación incluye visualizaciones avanzadas para representar datos complejos:

- **Mapa de Red**: Visualiza las conexiones entre diferentes componentes de la infraestructura
- **Gráficos de Vulnerabilidades**: Representa visualmente la distribución de vulnerabilidades
- **Cronología de Datos**: Visualiza datos temporales y eventos detectados

## 🔒 Seguridad

**IMPORTANTE**: Esta herramienta está diseñada para profesionales de seguridad y pruebas autorizadas. El uso indebido de estas herramientas puede ser ilegal. Sólo utiliza CyberScan Toolkit en sistemas para los que tienes permiso explícito.

## 🔄 Actualización

Para actualizar a la última versión:

```bash
git pull
pip install -r requirements.txt
./install_all_tools.sh  # Actualiza herramientas externas
```

## 🛠️ Solución de problemas comunes

- **Error al generar PDF**: Verifica que fpdf2 esté correctamente instalado
- **Herramienta no encontrada**: Ejecuta el script install_missing_tools.sh
- **Error de API**: Comprueba las claves API en el archivo api_keys.env

## 📜 Licencia

Software propietario - Todos los derechos reservados

## 📱 Contacto

Para preguntas o soporte, contacta: <EMAIL>

### Uso Responsable

Esta herramienta se proporciona únicamente con fines educativos y de pruebas de seguridad legítimas. Siempre:

1. Obtener la autorización adecuada antes de escanear cualquier objetivo
2. Seguir prácticas de divulgación responsable
3. Cumplir con todas las leyes y regulaciones aplicables

---

<div align="center">
<p>Desarrollado por <a href="https://github.com/stoja88">stoja88</a> © 2025</p>
</div> 