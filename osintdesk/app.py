import sys
import subprocess
import os
import datetime
from datetime import datetime
import uuid
import threading
import json
import logging
import queue # Added for SSE message queue
from flask import Flask, render_template, request, jsonify, Response, send_file, stream_with_context, redirect, url_for, flash, make_response
from fpdf import FPDF
from PIL import Image # Needed for chart dimensions in PDF
import matplotlib
matplotlib.use('Agg') # Use non-interactive backend for Matplotlib in web server
import matplotlib.pyplot as plt
from werkzeug.utils import secure_filename
import time
import requests # Added for API requests
from urllib.parse import urlparse # Added for target parsing
import re
import concurrent.futures
from functools import partial
import random
from matplotlib.colors import LinearSegmentedColormap
import numpy as np

# --- Logging Setup ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Flask App Setup ---
app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', 'dev_key_for_testing')

# --- Data stores ---
# Store for scan tasks
scan_tasks = {}
# Queue for SSE clients
clients = {}

# --- Tool definitions ---
# OSINT tools - for information gathering
OSINT_TOOLS = {
    "whois": {
        "display": "WHOIS",
        "description": "Obtiene información de registro del dominio",
        "simulation": True,
        "sim_func": "api_whois",
        "default_checked": True
    },
    "dig": {
        "display": "DIG",
        "description": "Consulta información de DNS",
        "simulation": True,
        "sim_func": "api_dig",
        "default_checked": False
    },
    "host": {
        "display": "Host",
        "description": "Análisis de registros DNS",
        "simulation": True,
        "sim_func": "api_host",
        "default_checked": False
    },
    "nslookup": {
        "display": "NS Lookup",
        "description": "Información de servidores de nombres",
        "simulation": True,
        "sim_func": "api_nslookup"
    },
    "theharvester": {
        "display": "The Harvester",
        "description": "Recolección de correos electrónicos",
        "simulation": True,
        "sim_func": "api_harvester"
    },
    "shodan": {
        "display": "Shodan",
        "description": "Información de dispositivos conectados",
        "simulation": True,
        "sim_func": "api_shodan",
        "default_checked": True
    },
    "sublist3r": {
        "display": "Sublist3r",
        "description": "Descubrimiento de subdominios",
        "simulation": True,
        "sim_func": "api_sublist3r",
        "default_checked": True
    },
    "wafw00f": {
        "display": "Wafw00f",
        "description": "Detección de firewalls de aplicaciones web",
        "simulation": True,
        "sim_func": "api_wafw00f"
    }
}

# Vulnerability tools - for security testing
VULN_TOOLS = {
    "nmap": {
        "display": "Nmap",
        "description": "Escaneo de puertos y servicios",
        "simulation": True,
        "sim_func": "api_nmap",
        "default_checked": True
    },
    "sslscan": {
        "display": "SSLScan",
        "description": "Análisis de configuración SSL/TLS",
        "simulation": True,
        "sim_func": "api_sslscan"
    },
    "nikto": {
        "display": "Nikto",
        "description": "Escáner de vulnerabilidades web",
        "simulation": True
    },
    "cmseeek": {
        "display": "CMSeeK",
        "description": "Detección y escaneo de CMS",
        "simulation": True
    },
    "sqlmap": {
        "display": "SQLMap",
        "description": "Detección de inyecciones SQL",
        "simulation": True
    }
}

# Helper function for safe filenames
def safe_filename(filename):
    """Create a safe filename by removing special characters."""
    # Replace any non-alphanumeric characters (except for dots, hyphens, and underscores) with underscores
    return re.sub(r'[^\w\.\-]', '_', str(filename))

# --- Routes ---
@app.route('/')
def index():
    """Renders the main page."""
    # Pass tool definitions to the template
    return render_template('index.html', osint_tools=OSINT_TOOLS, vuln_tools=VULN_TOOLS, scan_tasks=scan_tasks)

@app.route('/help')
def help_page():
    """Renders the help and documentation page."""
    return render_template('help.html')

#@app.route('/html_report/<task_id>')
def generate_html_report_legacy(task_id):
    """Generate and display an HTML report for a completed scan."""
    # NOTE: This is the primary implementation of the HTML report generator
    # The duplicate implementations at lines 2204 and 149 have been removed
    task = scan_tasks.get(task_id)
    if not task:
        return "Tarea no encontrada", 404
    if task['status'] != 'completed' and task['status'] != 'stopped':
        return "El escaneo debe finalizar antes de generar el reporte", 400
    
    target = task['target']
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # Get scan results
    osint_results = task.get('results_osint', {})
    vuln_results = task.get('results_vuln', {})
    
    # Calculate metrics for the report
    metrics = {
        'domains': random.randint(5, 15),  # Simulated values for demonstration
        'ips': random.randint(2, 8),
        'emails': random.randint(3, 12),
        'technologies': random.randint(4, 10),
        'vulnerabilities': random.randint(0, 7)
    }
    
    # Process results for the expected template format
    results = {
        'osint_results': osint_results,
        'vuln_results': vuln_results
    }
    
    # Simulate some additional data for the demonstration
    results['subdomains'] = []
    for i in range(metrics['domains']):
        results['subdomains'].append({
            'name': f"sub{i}.{target}" if not target.startswith(('http://', 'https://')) else f"sub{i}.{target.split('//')[1]}",
            'ip': f"192.168.1.{random.randint(1, 254)}"
        })
    
    results['emails'] = []
    for i in range(metrics['emails']):
        domain = target if not target.startswith(('http://', 'https://')) else target.split('//')[1]
        domain = domain.split('/')[0]  # Remove any path
        results['emails'].append({
            'address': f"user{i}@{domain}",
            'source': 'Email Harvester'
        })
    
    results['technologies'] = []
    tech_names = ['Apache', 'Nginx', 'PHP', 'MySQL', 'WordPress', 'jQuery', 'Bootstrap', 'React', 'Node.js', 'MongoDB']
    tech_categories = ['Web Server', 'Web Server', 'Programming Language', 'Database', 'CMS', 'JavaScript Library', 
                      'CSS Framework', 'JavaScript Framework', 'JavaScript Runtime', 'Database']
    for i in range(metrics['technologies']):
        results['technologies'].append({
            'name': tech_names[i % len(tech_names)],
            'version': f"{random.randint(1, 9)}.{random.randint(0, 9)}.{random.randint(0, 99)}",
            'category': tech_categories[i % len(tech_categories)]
        })
    
    results['vulnerabilities'] = []
    vuln_titles = ['Cross-Site Scripting (XSS)', 'SQL Injection', 'Outdated Software', 
                   'Insecure Direct Object References', 'Missing Security Headers', 
                   'Cross-Site Request Forgery (CSRF)', 'Insecure Cookie Configuration']
    vuln_descriptions = [
        'Reflected XSS vulnerability in search functionality',
        'SQL Injection vulnerability in login form',
        'Running outdated version with known security vulnerabilities',
        'Insecure direct object reference allows unauthorized access to resources',
        'Missing important security headers like X-Content-Type-Options, X-Frame-Options',
        'CSRF vulnerability in user profile update form',
        'Cookies set without secure and httpOnly flags'
    ]
    vuln_solutions = [
        'Implement proper input validation and output encoding',
        'Use prepared statements or ORM for database queries',
        'Update to the latest version and apply security patches',
        'Implement proper access controls and authorization checks',
        'Configure web server to add necessary security headers',
        'Implement anti-CSRF tokens in all forms',
        'Set secure and httpOnly flags for sensitive cookies'
    ]
    severities = ['high', 'high', 'medium', 'medium', 'low', 'medium', 'low']
    
    for i in range(min(metrics['vulnerabilities'], len(vuln_titles))):
        results['vulnerabilities'].append({
            'title': vuln_titles[i],
            'description': vuln_descriptions[i],
            'solution': vuln_solutions[i],
            'severity': severities[i],
            'references': 'https://owasp.org/Top10/'
        })
    
    # Render the HTML report
    return render_template('html_report.html',
                          target=target,
                          timestamp=timestamp,
                          metrics=metrics,
                          results=results)
                          
    
    return response

# --- API Functions ---
def api_whois(target):
    """Use an API for WHOIS lookup instead of command line"""
    try:
        url = f"https://api.apilayer.com/whois/query?domain={target}"
        headers = {'apikey': 'YOUR_API_KEY'} # Replace with your API key or use environment variable
        # Para desarrollo, siempre usamos la respuesta simulada
        logging.info(f"Simulando consulta WHOIS para {target}")
        return f"""=== WHOIS Information for {target} ===

Domain Information:
------------------
Domain Name: {target}
Registry Domain ID: 2336799_DOMAIN_COM-VRSN
Registrar WHOIS Server: whois.registrar.amazon.com
Registrar URL: http://registrar.amazon.com
Updated Date: 2022-04-15T13:42:23Z
Creation Date: 2019-05-09T20:13:59Z
Expiration Date: 2023-05-09T20:13:59Z

Registrar Information:
---------------------
Registrar: Amazon Registrar, Inc.
Registrar IANA ID: 468
Registrar Abuse Contact Email: <EMAIL>
Registrar Abuse Contact Phone: *************

Domain Status:
-------------
- clientTransferProhibited
- serverTransferProhibited
- clientUpdateProhibited
- serverUpdateProhibited
- clientDeleteProhibited
- serverDeleteProhibited

Name Servers:
------------
- NS-1780.AWSDNS-30.CO.UK
- NS-634.AWSDNS-15.NET
- NS-1530.AWSDNS-63.ORG
- NS-464.AWSDNS-58.COM

Last Update: 2022-07-02T04:06:22Z"""
    except Exception as e:
        return f"Error al realizar consulta WHOIS via API: {str(e)}"

def api_nslookup(target):
    """Use an API for NS lookup instead of command line"""
    try:
        logging.info(f"Simulando consulta NS Lookup para {target}")
        return f"""=== NS Lookup Results for {target} ===

Server Information:
-----------------
Server: dns.google
Address: *******

DNS Records:
-----------
Non-authoritative answer:
Name: {target}
Addresses:
- 2606:4700:20::681a:259
- 2606:4700:20::ac43:4a65
- 2606:4700:20::681a:359
- *************
- ***********
- ***********"""
    except Exception as e:
        return f"Error al realizar consulta NS via API: {str(e)}"

def api_shodan(target):
    """Use Shodan API for host lookup"""
    try:
        logging.info(f"Simulando consulta Shodan para {target}")
        return f"""=== Shodan Host Information for {target} ===

Basic Information:
----------------
IP: ************
Organization: Cloudflare, Inc.
Country: United States

Open Ports:
----------
- 80/tcp (HTTP)
- 443/tcp (HTTPS)

Vulnerabilities:
--------------
No vulnerabilities found in the last scan.

Last Update: {datetime.now().strftime('%Y-%m-%d')}"""
    except Exception as e:
        return f"Error al realizar consulta Shodan: {str(e)}"

def api_dig(target):
    """Simular el resultado de un comando dig."""
    try:
        logging.info(f"Simulando consulta DIG para {target}")
        return f"""=== DIG Results for {target} ===

Server Information:
------------------
; <<>> DiG 9.16.1 <<>> {target} ANY
;; global options: +cmd
;; Got answer:
;; ->>HEADER<<- opcode: QUERY, status: NOERROR, id: 12345
;; flags: qr rd ra; QUERY: 1, ANSWER: 5, AUTHORITY: 0, ADDITIONAL: 1

Answer Section:
--------------
{target}.     3600    IN    A    *************
{target}.     3600    IN    A    **************
{target}.     3600    IN    NS    ns1.cloudflare.com.
{target}.     3600    IN    NS    ns2.cloudflare.com.
{target}.     3600    IN    SOA   ns1.cloudflare.com. dns.cloudflare.com. 2345678901 10000 2400 604800 3600

Query time: 25 msec
SERVER: *******#53(*******)
WHEN: {datetime.now().strftime('%a %b %d %H:%M:%S %Y')}
MSG SIZE  rcvd: 120"""
    except Exception as e:
        return f"Error al simular consulta DIG: {str(e)}"

def api_host(target):
    """Simular el resultado de un comando host."""
    try:
        logging.info(f"Simulando consulta HOST para {target}")
        return f"""=== Host Records for {target} ===

DNS Records:
-----------
{target} has address *************
{target} has address **************
{target} has IPv6 address 2606:4700:3034::ac43:a4df
{target} has IPv6 address 2606:4700:3034::6815:1ec5
{target} name server ns1.cloudflare.com.
{target} name server ns2.cloudflare.com.
{target} mail is handled by 10 mx1.mailserver.com.
{target} mail is handled by 20 mx2.mailserver.com."""
    except Exception as e:
        return f"Error al simular consulta HOST: {str(e)}"

def api_harvester(target):
    """Simular el resultado de theHarvester."""
    try:
        logging.info(f"Simulando theHarvester para {target}")
        domain = target.replace("www.", "")
        return f"""=== Email Harvesting Results for {target} ===

Found Emails:
------------
info@{domain}
support@{domain}
contact@{domain}
admin@{domain}
marketing@{domain}

Found Hostnames:
--------------
www.{domain}
mail.{domain}
webmail.{domain}
api.{domain}
support.{domain}
admin.{domain}

Found URLs:
---------
https://{domain}/about
https://{domain}/contact
https://{domain}/services
https://{domain}/blog

The results shown here are simulated and are intended for testing purposes only."""
    except Exception as e:
        return f"Error al simular theHarvester: {str(e)}"

def api_wafw00f(target):
    """Simular el resultado de wafw00f."""
    try:
        logging.info(f"Simulando WAF detection para {target}")
        return f"""=== WAF Detection Results for {target} ===

WAF Detection Summary:
--------------------
URL: {target}

Detected WAFs:
- Cloudflare (Identified with confidence: high)

Additional Information:
---------------------
Status Code: 200
Server Header: cloudflare
Response Time: 125ms

The site {target} appears to be behind a Cloudflare WAF.
This is a simulated result for demonstration purposes."""
    except Exception as e:
        return f"Error al simular WAF Detection: {str(e)}"

def api_sublist3r(target):
    """Simular el resultado de Sublist3r."""
    try:
        logging.info(f"Simulando Sublist3r para {target}")
        # Generar algunos subdominios de ejemplo
        domain = target.replace("www.", "")
        subdomains = [
            f"www.{domain}",
            f"mail.{domain}",
            f"webmail.{domain}",
            f"portal.{domain}",
            f"admin.{domain}",
            f"support.{domain}",
            f"blog.{domain}",
            f"shop.{domain}",
            f"dev.{domain}",
            f"api.{domain}",
            f"cdn.{domain}",
            f"secure.{domain}",
            f"billing.{domain}",
            f"forum.{domain}"
        ]
        
        return f"""=== Subdomain Enumeration Results for {target} ===

Sublist3r v1.0
By Ahmed Aboul-Ela 

Finding subdomains for {domain}

Searching now...

Found Subdomains:
----------------
{chr(10).join(subdomains)}

Total Unique Subdomains Found: {len(subdomains)}

Execution time: 1.2 seconds
"""
    except Exception as e:
        return f"Error al simular Sublist3r: {str(e)}"

def api_nmap(target):
    """Simular el resultado de Nmap."""
    try:
        logging.info(f"Simulando Nmap para {target}")
        
        # Simular puertos comunes y sus servicios
        open_ports = [
            {"port": 21, "state": "open", "service": "ftp", "version": "vsftpd 3.0.3"},
            {"port": 22, "state": "open", "service": "ssh", "version": "OpenSSH 8.2p1 Ubuntu"},
            {"port": 80, "state": "open", "service": "http", "version": "Apache httpd 2.4.41"},
            {"port": 443, "state": "open", "service": "https", "version": "Apache httpd 2.4.41"},
            {"port": 3306, "state": "filtered", "service": "mysql", "version": "MySQL 5.7.33"}
        ]
        
        # Crear la salida formateada de Nmap
        port_details = "\n".join([
            f"{p['port']}/tcp {p['state']:8} {p['service']:10} {p['version']}" 
            for p in open_ports
        ])
        
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        return f"""=== Nmap Scan Results for {target} ===

Starting Nmap 7.91 at {current_time}
Nmap scan report for {target}
Host is up (0.0098s latency).
Not shown: 995 closed ports

PORT     STATE    SERVICE     VERSION
{port_details}

Service Info: OS: Linux; CPE: cpe:/o:linux:linux_kernel

Nmap done: 1 IP address (1 host up) scanned in 15.62 seconds
"""
    except Exception as e:
        return f"Error al simular Nmap: {str(e)}"

def api_sslscan(target):
    """Simular el resultado de sslscan."""
    try:
        logging.info(f"Simulando SSLScan para {target}")
        return f"""=== SSL/TLS Scan Results for {target} ===

Version: 2.0.10-static
OpenSSL 1.1.1l  (FIPS enabled)

CERTIFICATE INFORMATION:
----------------------
Subject:     CN={target}
Issuer:      C=US, O=Cloudflare, Inc., CN=Cloudflare Inc ECC CA-3
Not Before:  2025-01-01 00:00:00 UTC
Not After:   2026-01-01 23:59:59 UTC
Key:         EC 256 bits

TLS PROTOCOLS:
-------------
SSLv2               disabled
SSLv3               disabled
TLSv1.0             disabled
TLSv1.1             disabled
TLSv1.2             enabled
TLSv1.3             enabled

CIPHER SUITES (TLSv1.2):
----------------------
Preferred: TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256 (128 bits)
Accepted:  TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 (256 bits)
Accepted:  TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 (256 bits)

CIPHER SUITES (TLSv1.3):
----------------------
Preferred: TLS_AES_128_GCM_SHA256 (128 bits)
Accepted:  TLS_AES_256_GCM_SHA384 (256 bits)
Accepted:  TLS_CHACHA20_POLY1305_SHA256 (256 bits)

SECURITY ASSESSMENT:
-----------------
SECURE RENEGOTIATION:    supported
HEARTBLEED:              not vulnerable
ROBOT:                   not vulnerable
BEAST:                   not vulnerable (TLSv1.2+ only)
POODLE (SSLv3):          not vulnerable (SSLv3 disabled)
TLS FALLBACK SCSV:       supported

Scan completed in 0.42 seconds
"""
    except Exception as e:
        return f"Error al simular SSLScan: {str(e)}"

# --- Helper Function ---
def extract_relevant_target(input_target, for_shodan=False, with_port=False):
    """Extracts hostname/IP from a potential URL, optionally handling ports.

    Returns the relevant part of the target based on flags.
    - for_shodan: Returns IP/hostname (suitable for shodan host)
    - with_port: Tries to return host:port (suitable for sslscan)
    - default: Returns hostname/IP without port (suitable for whois, dig, sublist3r)
    """
    try:
        # If it looks like an IPv6 address, return as is (might need more robust check)
        if ':' in input_target and '.' not in input_target and '[' in input_target and ']' in input_target:
            return input_target # Assume valid IPv6 in brackets
        if ':' in input_target and '.' not in input_target:
             # Simple check for non-bracketed IPv6, less reliable
             parts = input_target.split(':')
             if len(parts) > 2: return input_target

        # If it looks like an IP address (v4)
        ip_part = input_target.split(':')[0]
        is_ip_v4 = all(c.isdigit() or c == '.' for c in ip_part) and ip_part.count('.') == 3
        if is_ip_v4:
            # For shodan, return IP without port unless port is explicitly given
            if for_shodan and ':' not in input_target:
                 return ip_part
            # For sslscan (with_port), return with port if specified, otherwise just IP
            elif with_port:
                 return input_target # Keep original potentially with port
            # Default (whois, etc.) return IP without port
            else:
                 return ip_part

        # If it looks like a URL
        parsed = urlparse(input_target)
        if parsed.scheme in ['http', 'https'] and parsed.netloc:
            hostname = parsed.netloc.split(':')[0]
            port = parsed.port
            # For shodan, return hostname only
            if for_shodan:
                return hostname
            # For sslscan (with_port), return hostname:port (defaulting if needed)
            elif with_port:
                return f"{hostname}:{port}" if port else f"{hostname}:{443 if parsed.scheme == 'https' else 80}"
            # Default (whois, etc.), return hostname only
            else:
                return hostname

        # Assume it's a domain name or something else (e.g., domain:port)
        hostname_or_ip = input_target.split(':')[0]
        # For shodan, return only the domain/IP part
        if for_shodan:
            return hostname_or_ip
        # For sslscan (with_port), keep potential port if present
        elif with_port:
             if ':' in input_target:
                 return input_target
             else:
                 # sslscan needs a port, default to 443 for domains? Or error?
                 # Let's default to 443 for sslscan if only domain is given
                 return f"{hostname_or_ip}:443"
        # Default (whois, etc.), return only domain/IP part
        else:
            return hostname_or_ip

    except Exception as e:
        logging.warning(f"Error parsing target '{input_target}': {e}. Falling back to original.")
        return input_target # Fallback

# --- Tool Execution Logic (Streaming Version) ---
class ToolRunner:
    @staticmethod
    def run_stream(cmd, timeout=300):
        """Runs a command and yields output lines (stdout/stderr) and status updates."""
        try:
            log_msg = f"Running command: {' '.join(cmd)}"
            logging.info(log_msg)
            yield {"event": "log", "data": log_msg}

            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT, # Combine stdout and stderr
                text=True,
                encoding='utf-8',
                errors='replace',
                bufsize=1, # Line buffered
                universal_newlines=True
            )

            # Read output line by line
            for line in process.stdout:
                 yield {"event": "result_chunk", "data": line}

            process.wait(timeout=timeout) # Wait for process to finish after reading stdout
            exit_code = process.returncode
            log_msg = f"Command '{' '.join(cmd)}' finished with exit code {exit_code}"
            logging.info(log_msg)
            yield {"event": "log", "data": log_msg}

            if exit_code != 0:
                # Yield a final error status for this tool
                yield {"event": "tool_error", "data": f"Error (Exit Code {exit_code}) executing {' '.join(cmd)}"}

            yield {"event": "tool_complete", "data": ""} # Signal tool completion

        except FileNotFoundError:
            error_msg = f"Error: Comando '{' '.join(cmd)}' no encontrado. Asegúrese de que la herramienta esté instalada y en la ruta del sistema (PATH)."
            logging.error(error_msg)
            yield {"event": "log", "data": error_msg}
            yield {"event": "tool_error", "data": error_msg}
            yield {"event": "tool_complete", "data": ""}
        except subprocess.TimeoutExpired:
            error_msg = f"Error: Comando '{' '.join(cmd)}' excedió el tiempo límite de {timeout} segundos."
            logging.error(error_msg)
            yield {"event": "log", "data": error_msg}
            yield {"event": "tool_error", "data": error_msg}
            try:
                process.kill()
                process.communicate() # Consume any remaining output
            except Exception as kill_e:
                logging.warning(f"Error trying to kill timed-out process: {kill_e}")
            yield {"event": "tool_complete", "data": ""}
        except Exception as e:
            error_msg = f"Error inesperado ejecutando '{' '.join(cmd)}': {str(e)}"
            logging.exception(f"Unexpected error during command execution for {' '.join(cmd)}")
            yield {"event": "log", "data": error_msg}
            yield {"event": "tool_error", "data": error_msg}
            yield {"event": "tool_complete", "data": ""}

    # Keep the old run method for non-streaming uses if needed, or remove it.
    # For now, let's assume we only need run_stream.
    # @staticmethod
    # def run(cmd, timeout=300): ... (Old implementation)

# --- Tool Definitions (REAL COMMANDS - USER MUST INSTALL TOOLS & VERIFY PATHS!) ---
# TODO: User needs to install these tools and update paths/API keys if necessary.
OSINT_TOOLS = {
    # Información básica y DNS
    "whois": {
        "display": "WHOIS Lookup",
        "cmd": ["whois", "{target}"],
        "timeout": 120,
        "api_func": None,
        "use_api": False,
        "description": "Consulta información de registro de dominios"
    },
    "nslookup": {
        "display": "NS Lookup",
        "cmd": ["nslookup", "{target}"],
        "timeout": 120,
        "api_func": None,
        "use_api": False,
        "description": "Consulta registros DNS del dominio"
    },
    "dig": {
        "display": "Análisis DIG (ANY)",
        "cmd": ["dig", "ANY", "{target}"],
        "timeout": 120,
        "api_func": None,
        "use_api": False,
        "description": "Obtiene todos los registros DNS disponibles"
    },
    "host": {
        "display": "Host Records",
        "cmd": ["host", "{target}"],
        "timeout": 120,
        "api_func": None,
        "use_api": False,
        "description": "Muestra información básica de host y DNS"
    },
    "dnsrecon": {
        "display": "DNS Recon", 
        "cmd": ["dnsrecon", "-d", "{target}", "-t", "std"], 
        "timeout": 180, 
        "default_checked": False,
        "description": "Realiza reconocimiento de DNS avanzado"
    }, # Reduced parameters to improve stability

    # Enumeración de subdominios
    "sublist3r": {
        "display": "Sublist3r Enum",
        "cmd": ["sublist3r", "-d", "{target}"],
        "timeout": 300, # Reduced timeout
        "default_checked": True,
        "api_func": None,
        "use_api": False,
        "description": "Busca subdominios utilizando diversas fuentes"
    },
    "amass": {
        "display": "Amass Enum (Pasivo)", 
        "cmd": ["amass", "enum", "-passive", "-d", "{target}", "-timeout", "3"], 
        "timeout": 300, # Reduced timeout
        "default_checked": False,
        "description": "Análisis pasivo de dominios y subdominios"
    }, # Added timeout parameter

    # Email y recopilación de información
    "theHarvester": {
        "display": "theHarvester (Emails)",
        "cmd": ["theHarvester", "-d", "{target}", "-l", "50", "-b", "bing"], # Reduced scope and sources
        "timeout": 180, # Reduced timeout
        "default_checked": True,
        "api_func": None,
        "use_api": False,
        "description": "Busca correos electrónicos y nombres de dominios"
    },
    "emailharvester": {"display": "EmailHarvester", "cmd": ["python3", "-m", "EmailHarvester", "-d", "{target}"], "timeout": 600, "default_checked": False}, # Needs domain
    "h8mail": {"display": "h8mail (Breach Check)", "cmd": ["h8mail", "-t", "{target}", "-c", "./temp/h8mail_config.ini"], "timeout": 300, "default_checked": False}, # Needs email

    # Inteligencia de amenazas y bases de datos externas
    "shodan": {
        "display": "Shodan Host Search",
        "cmd": ["shodan", "host", "{target_ip_or_domain}"],
        "timeout": 180,
        "default_checked": True,
        "api_func": None,
        "use_api": False
    },
    "censys": {"display": "Censys Search", "cmd": ["censys", "search", "{target}"], "timeout": 300, "default_checked": False}, # Needs domain/IP, API keys
    "vt_domain": {"display": "VirusTotal Domain", "cmd": ["curl", "-s", "https://www.virustotal.com/vtapi/v2/domain/report?apikey=YOUR_VT_API_KEY&domain={target}"], "timeout": 120, "default_checked": False}, # Needs domain, API Key

    # Redes sociales y fuentes públicas
    "sherlock": {"display": "Sherlock (Username)", "cmd": ["sherlock", "{target}", "--timeout", "10", "--print-found"], "timeout": 900, "default_checked": False}, # Needs username
    "socialscan": {"display": "SocialScan", "cmd": ["socialscan", "{target}", "--json"], "timeout": 180, "default_checked": False}, # Needs username/email
    "ghunt": {"display": "GHunt (Google)", "cmd": ["ghunt", "email", "{target}"], "timeout": 600, "default_checked": False}, # Needs Google email

    # Análisis web y capturas
    "waybackurls": {"display": "Wayback Machine URLs", "cmd": ["waybackurls", "{target}"], "timeout": 300, "default_checked": False}, # Needs domain
    "gau": {"display": "GetAllUrls", "cmd": ["gau", "{target}", "--threads", "5"], "timeout": 600, "default_checked": False}, # Needs domain
    "photon": {"display": "Photon Crawler", "cmd": ["python3", "-m", "photon", "-u", "{target_url}", "-l", "3", "-t", "10", "-o", "./temp/photon"], "timeout": 800, "default_checked": False}, # Needs URL
    "aquatone": {"display": "Aquatone Screenshots", "cmd": ["echo", "{target}", "|", "aquatone", "-out", "./temp/aquatone"], "timeout": 600, "default_checked": False}, # Needs domain

    # Filtración de datos y code leaks
    "github_dork": {"display": "GitHub Dorks", "cmd": ["curl", "-s", "https://api.github.com/search/code?q={target}+filename:config"], "timeout": 300, "default_checked": False}, # Needs domain/org name, consider API key
    "trufflehog": {"display": "TruffleHog (Secrets)", "cmd": ["trufflehog", "github", "--org", "{target}", "--json"], "timeout": 900, "default_checked": False}, # Needs org name
    "gitgraber": {"display": "GitGrabber", "cmd": ["python3", "/path/to/gitGraber.py", "-k", "{target}", "-s"], "timeout": 600, "default_checked": False}, # Needs keyword, update path

    # Análisis de metadata y multimedia
    "exiftool": {"display": "ExifTool (Metadata)", "cmd": ["exiftool", "-a", "-u", "-g1", "{target_file}"], "timeout": 180, "default_checked": False}, # Needs file path
    "metagoofil": {"display": "Metagoofil", "cmd": ["metagoofil", "-d", "{target}", "-t", "pdf,doc,xls,ppt,odp,ods,docx,xlsx,pptx", "-l", "10", "-n", "10", "-o", "./temp/metagoofil"], "timeout": 600, "default_checked": False}, # Needs domain
    "reverse_image": {"display": "Búsqueda Inversa Imágenes", "cmd": ["python3", "-m", "reverse_image_search", "{target_file}", "--output", "./temp/reverse_image_results.json"], "timeout": 300, "default_checked": False}, # Needs image path
}

VULN_TOOLS = {
    # Basic Nmap scans
    "nmap_quick": {
        "display": "Nmap (Top Puertos)",
        "cmd": ["nmap", "-sV", "-T4", "--top-ports", "1000", "{target}"], # Actual command
        "timeout": 600,
        "default_checked": True,
        "api_func": None,
        "use_api": False # Use actual command
    },
    "nmap_full": {"display": "Nmap (Full TCP)", "cmd": ["nmap", "-sV", "-T4", "-p-", "--min-rate=500", "{target}"], "timeout": 3600, "default_checked": False}, # Needs IP/Domain
    "nmap_vuln": {"display": "Nmap (Vuln Script)", "cmd": ["nmap", "-sV", "--script=vuln", "-T4", "--top-ports", "1000", "{target}"], "timeout": 1200, "default_checked": False}, # Needs IP/Domain

    # === HERRAMIENTAS AVANZADAS DE PENTESTING ===
    # Pentesting Web Avanzado
    "sqlmap": {"display": "SQLMap (Inyección SQL)", "cmd": ["sqlmap", "-u", "{target_url}", "--forms", "--batch", "--random-agent"], "timeout": 1200, "category": "advanced", "default_checked": False},
    "xsshunter": {"display": "XSS Hunter", "cmd": ["python3", "-c", "print('XSS Hunter setup needed for {target_url}');"], "timeout": 600, "category": "advanced", "default_checked": False}, # Placeholder, requires setup
    "jwt_tool": {"display": "JWT Pentesting", "cmd": ["jwt_tool", "{jwt_token}", "-X", "a"], "timeout": 300, "category": "advanced", "default_checked": False}, # Example, needs token input
    "nosqlmap": {"display": "NoSQLMap", "cmd": ["nosqlmap", "--url", "{target_url}", "--test"], "timeout": 900, "category": "advanced", "default_checked": False}, # Example command

    # Fuerza Bruta
    "hydra_ssh": {"display": "Hydra SSH Bruteforce", "cmd": ["hydra", "-L", "users.txt", "-P", "pass.txt", "ssh://{target}"], "timeout": 3600, "category": "brute_force", "default_checked": False}, # Needs user/pass lists
    "hashcat": {"display": "Hashcat", "cmd": ["hashcat", "-m", "0", "hash_file.txt", "wordlist.txt"], "timeout": 3600, "category": "brute_force", "default_checked": False}, # Needs hash file/wordlist
    "medusa": {"display": "Medusa Brute Force", "cmd": ["medusa", "-h", "{target}", "-U", "users.txt", "-P", "pass.txt", "-M", "ssh"], "timeout": 1800, "category": "brute_force", "default_checked": False}, # Needs user/pass lists

    # Análisis de Redes
    "responder": {"display": "Responder (MITM)", "cmd": ["responder", "-I", "eth0", "-v"], "timeout": 900, "category": "network", "default_checked": False}, # Needs interface, careful!
    "bettercap": {"display": "Bettercap", "cmd": ["bettercap", "-iface", "eth0", "-caplet", "http-ui"], "timeout": 1200, "category": "network", "default_checked": False}, # Needs interface, careful!

    # Web application scanners
    "nikto": {"display": "Nikto Scan", "cmd": ["nikto", "-h", "{target_url}"], "timeout": 900, "default_checked": False}, # Needs URL
    # Nuclei requires installation and template updates (`nuclei -update-templates`)
    "nuclei": {"display": "Nuclei (Defecto)", "cmd": ["nuclei", "-u", "{target_url}", "-silent", "-severity", "critical,high,medium"], "timeout": 1800, "default_checked": True}, # Needs URL
    "nuclei_full": {"display": "Nuclei (Full Scan)", "cmd": ["nuclei", "-u", "{target_url}", "-silent"], "timeout": 2400, "default_checked": False}, # Needs URL

    # SSL/TLS and WAF detection
    "ssl_scan": {
        "display": "SSL/TLS Scan (sslscan)",
        "cmd": ["sslscan", "--no-colour", "{target_with_port}"],
        "timeout": 300,
        "default_checked": True,
        "api_func": None,
        "use_api": False
    },
    "testssl": {"display": "TestSSL.sh", "cmd": ["testssl.sh", "--severity=HIGH", "{target_url}"], "timeout": 900, "default_checked": False}, # Needs URL/IP:Port
    "waf_detection": {
        "display": "Detección WAF (wafw00f)",
        "cmd": ["wafw00f", "{target_url}"],
        "timeout": 300,
        "default_checked": True,
        "api_func": None,
        "use_api": False
    },

    # CMS and specific app scanners
    "cms_scan": {"display": "CMS Scan (CMSeeK)", "cmd": ["python3", os.path.join(os.path.expanduser("~"), "CMSeeK", "cmseek.py"), "-u", "{target_url}", "--batch", "--follow-redirect"], "timeout": 600, "default_checked": False}, # Needs URL, update path
    "wpscan": {"display": "WordPress Scan", "cmd": ["wpscan", "--url", "{target_url}", "--api-token", "YOUR_WPScan_API_TOKEN", "--enumerate", "vp,vt,dbe"], "timeout": 900, "default_checked": False}, # Needs WordPress URL, API Token
    "joomscan": {"display": "Joomla Scan", "cmd": ["joomscan", "--url", "{target_url}"], "timeout": 600, "default_checked": False}, # Needs Joomla URL
}

# --- PDF Reporting (Adapted for Spanish) ---
class ReportPDF(FPDF):
    def __init__(self, company_name="", report_title="Reporte de Análisis de Seguridad"):
        super().__init__()
        self.WIDTH = 210
        self.HEIGHT = 297
        self.company_name = company_name
        self.report_title_text = report_title
        self.set_auto_page_break(auto=True, margin=15)
        self.set_left_margin(20)  # Aumentar el margen izquierdo
        self.set_right_margin(20) # Aumentar el margen derecho
        # Definir colores para fpdf2 (RGB de 0-255)
        self.header_color = (220, 220, 220)
        self.title_color = (70, 130, 180)
        self.text_color = (0, 0, 0)
        
        # Usar fuentes incorporadas para evitar advertencias
        self.set_font('Helvetica', '', 10)
    
    def header(self):
        self.set_font('Helvetica', 'B', 10)
        self.set_fill_color(self.header_color[0], self.header_color[1], self.header_color[2])
        # Usar new_x y new_y en lugar de ln
        from fpdf.enums import XPos, YPos
        self.cell(0, 8, self.report_title_text, border=0, new_x=XPos.LMARGIN, new_y=YPos.NEXT, align='C', fill=True)
        if self.company_name:
            self.set_font('Helvetica', 'I', 8)
            self.cell(0, 5, f"Preparado para: {self.company_name}", border=0, new_x=XPos.LMARGIN, new_y=YPos.NEXT, align='C')
        # Aumentar el espacio después del encabezado
        self.set_y(self.get_y() + 8)

    def footer(self):
        self.set_y(-15)
        self.set_font('Helvetica', 'I', 8)
        self.set_text_color(128, 128, 128)
        # Corregir la deprecación
        from fpdf.enums import XPos, YPos
        self.cell(0, 10, f'Página {self.page_no()}/{{nb}}', 0, new_x=XPos.RIGHT, new_y=YPos.TOP, align='C')

    def chapter_title(self, title):
        from fpdf.enums import XPos, YPos
        self.set_font('Helvetica', 'B', 14)
        self.set_text_color(self.title_color[0], self.title_color[1], self.title_color[2])
        self.cell(0, 10, title, 0, new_x=XPos.LMARGIN, new_y=YPos.NEXT, align='L')
        self.set_draw_color(self.title_color[0], self.title_color[1], self.title_color[2])
        self.line(self.get_x(), self.get_y(), self.get_x() + self.WIDTH - 40, self.get_y())
        # Aumentar el espacio después del título
        self.set_y(self.get_y() + 8)
        self.set_text_color(self.text_color[0], self.text_color[1], self.text_color[2])

    def chapter_body(self, body):
        self.set_font('Helvetica', '', 9)
        try:
            # Limitar el contenido para evitar errores de espacio
            if len(body) > 15000:  # Si el texto es muy largo, truncarlo
                safe_body = body[:15000] + "\n\n[Contenido truncado debido a limitaciones de espacio...]"
            else:
                safe_body = body
            
            # Codificar de manera segura
            safe_body = safe_body.encode('latin-1', 'replace').decode('latin-1')
        except Exception:
            safe_body = "[Error al procesar contenido]"
        
        # Procesar línea por línea con una longitud máxima
        lines = safe_body.split('\n')
        max_line_length = 80  # Limitar longitud de línea
        
        for line in lines:
            if len(line) > max_line_length:
                # Dividir líneas largas
                chunks = [line[i:i+max_line_length] for i in range(0, len(line), max_line_length)]
                for chunk in chunks:
                    self.multi_cell(0, 4, chunk)
            else:
                self.multi_cell(0, 4, line)
                
        self.ln(3)

    def add_title_page(self, target, timestamp):
        self.add_page()
        self.alias_nb_pages()
        self.set_font('Helvetica', 'B', 24)  # Reducir tamaño de fuente
        self.set_y(80)
        self.set_text_color(self.title_color[0], self.title_color[1], self.title_color[2])
        from fpdf.enums import XPos, YPos
        self.cell(0, 20, self.report_title_text, 0, new_x=XPos.LMARGIN, new_y=YPos.NEXT, align='C')
        
        self.set_font('Helvetica', '', 14)  # Reducir tamaño
        self.set_text_color(self.text_color[0], self.text_color[1], self.text_color[2])
        
        # Acortar target si es muy largo
        if len(target) > 40:
            target = target[:37] + "..."
            
        self.cell(0, 15, f"Objetivo: {target}", 0, new_x=XPos.LMARGIN, new_y=YPos.NEXT, align='C')
        self.cell(0, 15, f"Fecha Escaneo: {timestamp}", 0, new_x=XPos.LMARGIN, new_y=YPos.NEXT, align='C')
        if self.company_name:
            self.cell(0, 15, f"Preparado para: {self.company_name}", 0, new_x=XPos.LMARGIN, new_y=YPos.NEXT, align='C')
        
        self.set_y(self.HEIGHT - 50)
        self.set_font('Helvetica', 'I', 10)
        self.set_text_color(128, 128, 128)
        self.multi_cell(0, 5, "Este reporte fue generado automáticamente. Los hallazgos requieren verificación e interpretación manual por profesionales de seguridad cualificados.", align='C')

    def add_summary(self, target, timestamp, tools_used, results):
        self.add_page()
        self.chapter_title("Resumen Ejecutivo")
        self.set_font('Helvetica', '', 11)
        
        # Crear un resumen con un formato más seguro
        self.cell(0, 5, "Información General", ln=1)
        self.ln(2)
        
        # Acortar target si es muy largo
        if len(target) > 40:
            target = target[:37] + "..."
            
        self.cell(30, 5, "Objetivo:", 0, 0)
        self.cell(0, 5, target, 0, 1)
        
        self.cell(30, 5, "Fecha:", 0, 0)
        self.cell(0, 5, timestamp, 0, 1)
        
        self.ln(5)
        self.cell(0, 5, "Herramientas Utilizadas:", 0, 1)
        self.ln(2)
        
        # Obtener nombres de herramientas de manera más segura
        tool_names = []
        for t in results.get("osint", {}):
            display = OSINT_TOOLS.get(t, {}).get('display', t)
            if display and len(display) < 40:  # Limitar longitud
                tool_names.append(display)
        
        for t in results.get("vuln", {}):
            display = VULN_TOOLS.get(t, {}).get('display', t)
            if display and len(display) < 40:  # Limitar longitud
                tool_names.append(display)
        
        # Mostrar herramientas en grupos pequeños
        for i in range(0, len(tool_names), 3):
            group = tool_names[i:i+3]
            self.cell(10, 5, "•", 0, 0)
            self.cell(0, 5, ", ".join(group), 0, 1)
        
        self.ln(5)
        self.cell(0, 5, "Resumen de Hallazgos:", 0, 1)
        self.ln(2)
        
        osint_items = len(results.get("osint", {}))
        vuln_items = len(results.get("vuln", {}))
        
        self.cell(10, 5, "•", 0, 0)
        self.cell(0, 5, f"Módulos OSINT ejecutados: {osint_items}", 0, 1)
        
        self.cell(10, 5, "•", 0, 0)
        self.cell(0, 5, f"Módulos de Vulnerabilidad ejecutados: {vuln_items}", 0, 1)
        
        if vuln_items > 0:
            self.cell(10, 5, "•", 0, 0)
            self.cell(0, 5, "Hallazgos simulados Críticos/Altos: 7", 0, 1)
            
            self.cell(10, 5, "•", 0, 0)
            self.cell(0, 5, "Hallazgos simulados Medios/Bajos: 20", 0, 1)
        
        self.ln(5)
        self.set_font('Helvetica', 'I', 10)
        self.multi_cell(0, 5, "Descargo de Responsabilidad: Las herramientas automatizadas proporcionan información inicial pero pueden contener falsos positivos u omitir vulnerabilidades complejas. La verificación manual es crucial.")
        self.ln(5)

# --- Flask App Setup ---
app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads/'
app.config['SECRET_KEY'] = os.urandom(24) # Generate a random secret key

# Ensure upload folder exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs("temp", exist_ok=True) # Ensure temp dir exists early

# In-memory storage for scan tasks & SSE queues
scan_tasks = {} # Structure will be enhanced in start_scan
clients = {} # Dictionary to hold queues for active SSE clients: {task_id: queue.Queue()}

# --- Scan Execution Thread ---
def run_scan_thread(task_id, target, tools, tool_definitions):
    task = scan_tasks[task_id]
    task['status'] = 'running'
    task['log'].append(f"[{datetime.now().strftime('%H:%M:%S')}] Iniciando escaneo...")
    results = {}
    total_tools = len(tools)
    completed_tools = 0

    for tool_name in tools:
        if task_id not in scan_tasks or scan_tasks[task_id]['status'] == 'stopped':
            logging.info(f"Scan task {task_id} aborted.")
            task['log'].append(f"[{datetime.now().strftime('%H:%M:%S')}] Escaneo abortado.")
            task['status'] = 'stopped'
            return # Stop thread

        if tool_name in tool_definitions:
            tool_info = tool_definitions[tool_name]
            display_name = tool_info.get("display", tool_name.capitalize())
            cmd_template = tool_info.get("cmd")
            timeout = tool_info.get("timeout", 300)
            is_simulated = tool_info.get("simulated", False)
            target_url = target # Placeholder, adjust if needed for specific tools
            if not target.startswith(('http://', 'https://')):
                is_ip = all(c.isdigit() or c == '.' for c in target)
                target_url = f"https://{target}" if is_ip else f"http://{target}"
            
            log_msg = f"[{datetime.now().strftime('%H:%M:%S')}] Ejecutando {display_name}..."
            task['log'].append(log_msg)
            logging.info(f"Task {task_id}: {log_msg}")
            
            output = ""
            if is_simulated:
                sim_func = tool_info.get("simulation_func")
                if sim_func:
                    output = sim_func(target)
                    log_msg = f"[{datetime.now().strftime('%H:%M:%S')}] {display_name} (Simulado) completado."
                    task['log'].append(log_msg)
                else:
                    output = f"Función de simulación no definida para {tool_name}"
                    log_msg = f"[{datetime.now().strftime('%H:%M:%S')}] Error simulando {display_name}."
                    task['log'].append(log_msg)
            elif cmd_template:
                cmd = [part.replace("{target}", target).replace("{target_url}", target_url) for part in cmd_template]
                output = ToolRunner.run_stream(cmd, timeout=timeout)
                if output.startswith("Error:"):
                     log_msg = f"[{datetime.now().strftime('%H:%M:%S')}] {display_name} encontró un error."
                     task['log'].append(log_msg)
                else:
                    log_msg = f"[{datetime.now().strftime('%H:%M:%S')}] {display_name} completado."
                    task['log'].append(log_msg)
            else:
                 output = "Comando de herramienta no definido."
                 log_msg = f"[{datetime.now().strftime('%H:%M:%S')}] Error ejecutando {display_name}: Comando faltante."
                 task['log'].append(log_msg)
            
            results[tool_name] = output
            task['log'].append(f"Salida de {display_name}:\n{output}\n---")
        else:
            log_msg = f"[{datetime.now().strftime('%H:%M:%S')}] Advertencia: Herramienta '{tool_name}' no definida."
            task['log'].append(log_msg)
            logging.warning(f"Task {task_id}: Tool '{tool_name}' not defined.")
            
        completed_tools += 1
        task['progress'] = int((completed_tools / total_tools) * 100)

    task['results'] = results
    task['status'] = 'completed'
    task['progress'] = 100
    task['log'].append(f"[{datetime.now().strftime('%H:%M:%S')}] Escaneo completado.")
    logging.info(f"Scan task {task_id} completed.")


# --- Flask Routes ---
@app.route('/')
def index():
    """Renders the main page."""
    # Pass tool definitions to the template
    return render_template('index.html', osint_tools=OSINT_TOOLS, vuln_tools=VULN_TOOLS, scan_tasks=scan_tasks)

@app.route('/favicon.ico')
def favicon():
    """Genera un favicon simple en memoria para evitar errores 404."""
    import io
    from PIL import Image, ImageDraw
    
    # Crear una imagen de 16x16 píxeles
    img = Image.new('RGBA', (32, 32), color=(0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Dibujar un círculo cian
    draw.ellipse((4, 4, 28, 28), fill=(6, 182, 212, 255))
    
    # Dibujar un pequeño círculo interno azul oscuro
    draw.ellipse((12, 12, 20, 20), fill=(12, 74, 110, 255))
    
    # Convertir a formato ICO
    ico_output = io.BytesIO()
    img.save(ico_output, format='ICO')
    ico_output.seek(0)
    
    return Response(ico_output.getvalue(), mimetype='image/x-icon')

@app.route('/visualization')
def visualization():
    """Renderiza la página de visualización 3D."""
    # Obtener los escaneos recientes para el listado
    recent_scans = []
    try:
        # Limitar a los 10 escaneos más recientes
        for task_id, task in list(scan_tasks.items())[-10:]:
            # Solo incluir tareas completadas o detenidas
            if task.get('status') in ['completed', 'stopped']:
                recent_scans.append({
                    'id': task_id,
                    'target': task.get('target', 'Unknown'),
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M')
                })
    except Exception as e:
        logging.error(f"Error al obtener scans recientes: {e}")
    
    # Invertir para mostrar los más recientes primero
    recent_scans.reverse()
    
    return render_template('visualization.html', recent_scans=recent_scans)

@app.route('/visualization_data/<scan_id>')
def visualization_data(scan_id):
    """Obtiene los datos para la visualización."""
    task = scan_tasks.get(scan_id)
    if not task:
        return jsonify({"error": "Escaneo no encontrado"}), 404
    
    # Preparar datos para visualización
    osint_results = task.get('results_osint', {})
    vuln_results = task.get('results_vuln', {})
    
    # Contar herramientas
    osint_count = len(osint_results)
    vuln_count = len(vuln_results)
    
    # Simplificar hallazgos para demo
    findings = {
        'critical': 0,
        'high': 0,
        'medium': 0,
        'low': 0,
        'info': 0
    }
    
    # Simulación - si hay resultados de vulnerabilidades, generar estadísticas simuladas
    if vuln_count > 0:
        findings['critical'] = 1
        findings['high'] = 3
        findings['medium'] = 5
        findings['low'] = 7
        findings['info'] = 10
    
    return jsonify({
        'scan_id': scan_id,
        'target': task.get('target', 'Unknown'),
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M'),
        'status': task.get('status', 'unknown'),
        'osint_count': osint_count,
        'vuln_count': vuln_count,
        'findings': findings
    })

@app.route('/help')
def help_page():
    """Renders the help and documentation page."""
    return render_template('help.html')

@app.route('/start_scan', methods=['POST'])
def start_scan():
    try:
        # Aceptar tanto JSON como form data
        if request.is_json:
            data = request.json
            target = data.get('target')
            selected_tools = data.get('tools', [])
            if not isinstance(selected_tools, list):
                selected_tools = [selected_tools]
        else:
            # Procesar form data
            target = request.form.get('target')
            selected_tools = request.form.getlist('tools')

        if not target:
            error_message = "El objetivo es requerido para el análisis"
            return render_template('error.html', error_message=error_message)

        if not selected_tools:
            error_message = "Selecciona al menos una herramienta"
            return render_template('error.html', error_message=error_message)

        # Separar herramientas en OSINT y vulnerabilidades
        selected_osint = [tool for tool in selected_tools if tool in OSINT_TOOLS]
        selected_vuln = [tool for tool in selected_tools if tool in VULN_TOOLS]

        task_id = str(uuid.uuid4())
        # Crear la cola de mensajes para el stream SSE de esta tarea
        clients[task_id] = queue.Queue()

        # Agregar timestamp de inicio para cálculos de duración
        start_time = datetime.now()
        
        scan_tasks[task_id] = {
            'id': task_id,
            'target': target,
            'osint_tools': selected_osint,
            'vuln_tools': selected_vuln,
            'status': 'queued',
            'progress': 0,
            'log': [],
            'results_osint': {},
            'results_vuln': {},
            'current_tool': None,
            'thread_osint': None,
            'thread_vuln': None,
            'start_time': start_time,  # Nuevo campo para el tiempo de inicio
            'end_time': None  # Se establecerá cuando el análisis termine
        }

        logging.info(f"Iniciando tarea de análisis {task_id} para objetivo {target}. Cola SSE creada.")
        # Poner mensaje inicial en la cola
        clients[task_id].put({"event": "status", "data": {"status": "queued", "progress": 0}})

        # Iniciar hilos de análisis
        if selected_osint:
            thread_osint = threading.Thread(target=run_scan_phase, args=(task_id, target, selected_osint, OSINT_TOOLS, 'results_osint'))
            scan_tasks[task_id]['thread_osint'] = thread_osint
            thread_osint.start()
            
        if selected_vuln:
            thread_vuln = threading.Thread(target=run_scan_phase, args=(task_id, target, selected_vuln, VULN_TOOLS, 'results_vuln'))
            scan_tasks[task_id]['thread_vuln'] = thread_vuln
            thread_vuln.start()
            
        if not selected_osint and not selected_vuln:
            del scan_tasks[task_id]
            if task_id in clients:
                del clients[task_id]  # Limpieza de cola en caso de error
            error_message = "Error inesperado al iniciar el análisis"
            return render_template('error.html', error_message=error_message)

        # Redirigir a la página de streaming
        return redirect(url_for('stream', task_id=task_id, target=target))
    
    except Exception as e:
        logging.exception("Error en start_scan")
        error_message = f"Error al iniciar el análisis: {str(e)}"
        return render_template('error.html', error_message=error_message)

def run_scan_phase(task_id, target, tools, tool_definitions, result_key):
    task = scan_tasks.get(task_id)
    if not task or task_id not in clients:
        logging.error(f"Task {task_id} or its queue not found for phase {result_key}.")
        return

    task_queue = clients[task_id]
    send_event = lambda et, d: task_queue.put({"event": et, "data": d}) # Simplified send_event

    current_phase_name = "OSINT" if result_key == 'results_osint' else "Vulnerabilidades"
    task['status'] = f'running_{result_key}'
    send_event("status", {"status": task['status'], "progress": task['progress'], "message": f"Iniciando fase: {current_phase_name}"})
    logging.info(f"Task {task_id}: Starting phase {result_key}")

    results = {}
    total_tools_in_phase = len(tools)
    completed_tools_in_phase = 0
    is_osint_phase = (result_key == 'results_osint')
    has_both_phases = task.get('osint_tools') and task.get('vuln_tools') # Use .get for safety
    base_progress = 50 if not is_osint_phase and has_both_phases else 0
    progress_per_tool = (50 if has_both_phases else 100) / total_tools_in_phase if total_tools_in_phase > 0 else 0

    # OPTIMIZACIÓN ADICIONAL: Limitar el número máximo de herramientas
    # Si hay demasiadas herramientas seleccionadas, podría causar sobrecarga
    max_tools_to_run = 8  # Limitar a máximo 8 herramientas por fase
    if len(tools) > max_tools_to_run:
        limited_tools = tools[:max_tools_to_run]
        log_msg = f"⚠️ Limitando a {max_tools_to_run} herramientas para evitar sobrecarga del sistema"
        send_event("log", {"message": log_msg, "level": "warning"})
        logging.warning(f"Task {task_id}: Limiting to {max_tools_to_run} tools to prevent system overload")
        tools = limited_tools

    # Función para ejecutar una herramienta individual
    def run_single_tool(tool_name):
        nonlocal completed_tools_in_phase
        nonlocal task

        if task_id not in scan_tasks or scan_tasks.get(task_id, {}).get('status') == 'stopped':
            logging.info(f"Scan phase {result_key} for tool {tool_name} aborted by user.")
            send_event("log", {"message": f"Herramienta {tool_name} abortada por el usuario.", "level": "warning"})
            return None, None

        if tool_name not in tool_definitions:
            log_msg = f"Advertencia: Herramienta '{tool_name}' no definida."
            send_event("log", {"message": log_msg, "level": "warning"})
            logging.warning(f"Task {task_id}: Tool '{tool_name}' not defined.")
            return tool_name, ""

        tool_info = tool_definitions[tool_name]
        display_name = tool_info.get("display", tool_name.capitalize())
        cmd_template = tool_info.get("cmd")
        timeout = tool_info.get("timeout", 180) # Reduced default timeout
        use_api = tool_info.get("use_api", False)
        api_func = tool_info.get("api_func", None)

        task['current_tool'] = display_name
        send_event("tool_start", {"tool_name": tool_name, "display_name": display_name})
        send_event("log", {"message": f"Ejecutando {display_name}...", "level": "info"})
        logging.info(f"Task {task_id}: Running tool {display_name}")

        tool_output_chunks = []
        tool_final_status = "success"

        # OPTIMIZACIÓN: Verificar si la herramienta está instalada antes de intentar ejecutarla
        if cmd_template and not use_api:
            cmd_first_part = cmd_template[0]
            # Verificar si el comando principal está disponible en el sistema
            if cmd_first_part not in ['echo', 'curl', 'python3', 'python', 'dig', 'nslookup', 'host', 'whois']:
                # Comandos que no son estándar necesitan verificación
                try:
                    check_cmd = ['which', cmd_first_part]
                    result = subprocess.run(check_cmd, capture_output=True, text=True)
                    if result.returncode != 0:
                        error_msg = f"La herramienta '{cmd_first_part}' no está instalada o no se encuentra en el PATH."
                        send_event("log", {"message": error_msg, "level": "error"})
                        send_event("tool_error", {"tool_name": tool_name, "error": error_msg})
                        tool_final_status = "error"
                        send_event("tool_complete", {"tool_name": tool_name, "status": tool_final_status})
                        # Skip execution
                        return tool_name, error_msg
                except Exception as e:
                    logging.warning(f"Error checking tool availability: {e}")

        # OPTIMIZACIÓN ADICIONAL: Verificar disponibilidad de memoria antes de ejecutar herramientas intensivas
        if tool_name in ['amass', 'subfinder', 'sublist3r', 'nmap_full', 'nmap_vuln']:
            try:
                # Intentar obtener información de memoria del sistema (funciona en sistemas Unix/Linux)
                import os
                import psutil
                
                # Obtener memoria disponible en MB
                available_memory = psutil.virtual_memory().available / 1024 / 1024
                
                # Si hay menos de 500MB disponibles, advertir y posiblemente saltar esta herramienta
                if available_memory < 500:
                    memory_warning = f"⚠️ Poca memoria disponible ({int(available_memory)}MB) para ejecutar {display_name}. Puede causar inestabilidad."
                    send_event("log", {"message": memory_warning, "level": "warning"})
                    logging.warning(f"Task {task_id}: Low memory available for intensive tool {tool_name}")
                    
                    # Si es extremadamente baja, mejor no ejecutar
                    if available_memory < 200:
                        skip_msg = f"❌ Memoria insuficiente para ejecutar {display_name} de forma segura. Saltando herramienta."
                        send_event("log", {"message": skip_msg, "level": "error"})
                        send_event("tool_error", {"tool_name": tool_name, "error": "Memoria insuficiente"})
                        tool_final_status = "error"
                        send_event("tool_complete", {"tool_name": tool_name, "status": tool_final_status})
                        return tool_name, "Memoria insuficiente para ejecutar esta herramienta"
            except ImportError:
                logging.warning("psutil no está instalado. No se puede verificar la memoria disponible.")
            except Exception as e:
                logging.warning(f"Error al verificar memoria: {e}")

        # Usar la API si está disponible y habilitada
        if use_api and api_func:
            try:
                # Procesar el target según el tipo de herramienta
                target_for_api = target
                if "target_ip_or_domain" in str(cmd_template):
                    target_for_api = extract_relevant_target(target, for_shodan=True)
                elif "target_with_port" in str(cmd_template):
                    target_for_api = extract_relevant_target(target, with_port=True)
                elif "target" in str(cmd_template):
                    target_for_api = extract_relevant_target(target)
                
                # Llamar a la función API
                api_result = api_func(target_for_api)
                
                # Enviar resultado al cliente
                log_msg = f"Utilizando API para {display_name}"
                send_event("log", {"message": log_msg, "level": "info"})
                
                # Enviar cada línea por separado para mejor visualización
                if isinstance(api_result, str):
                    for line in api_result.split('\n'):
                        send_event("result_chunk", {"tool_name": tool_name, "chunk": line + '\n'})
                else:
                    send_event("result_chunk", {"tool_name": tool_name, "chunk": str(api_result)})
                
                # Almacenar el resultado
                tool_output_chunks = [api_result]
                
                # Indicar éxito
                send_event("tool_complete", {"tool_name": tool_name, "status": "success"})
                send_event("log", {"message": f"{display_name} completado con éxito (via API).", "level": "success"})
            
            except Exception as api_error:
                error_msg = f"Error al usar API para {display_name}: {str(api_error)}"
                logging.exception(f"Task {task_id}: API execution error")
                tool_output_chunks.append(error_msg)
                send_event("log", {"message": error_msg, "level": "error"})
                send_event("tool_error", {"tool_name": tool_name, "error": error_msg})
                tool_final_status = "error"
                send_event("tool_complete", {"tool_name": tool_name, "status": tool_final_status})
        
        elif not cmd_template:
            # Handle missing command template (shouldn't happen with new defs)
            error_msg = f"Comando no definido para {display_name}."
            tool_output_chunks.append(error_msg)
            send_event("log", {"message": error_msg, "level": "error"})
            send_event("tool_error", {"tool_name": tool_name, "error": error_msg})
            tool_final_status = "error"
            send_event("tool_complete", {"tool_name": tool_name, "status": tool_final_status})
        else:
            # Prepare command using the correct target format
            cmd = []
            try:
                # Determine target format based on placeholders in cmd_template
                cmd_str = " ".join(cmd_template)
                target_for_cmd = target # Default
                
                # OPTIMIZACIÓN: Detectar y substituir {target_file} sin parámetro real
                if "{target_file}" in cmd_str:
                    target_file_error = f"Error: No hay archivo objetivo proporcionado para {display_name}"
                    send_event("log", {"message": target_file_error, "level": "error"})
                    send_event("tool_error", {"tool_name": tool_name, "error": target_file_error})
                    tool_final_status = "error"
                    send_event("tool_complete", {"tool_name": tool_name, "status": tool_final_status})
                    return tool_name, target_file_error
                
                if "{target_ip_or_domain}" in cmd_str:
                    target_for_cmd = extract_relevant_target(target, for_shodan=True)
                elif "{target_with_port}" in cmd_str:
                    target_for_cmd = extract_relevant_target(target, with_port=True)
                elif "{target}" in cmd_str:
                    target_for_cmd = extract_relevant_target(target)

                # Determine target_url (always needed for replacement, even if not used by specific command)
                target_url = target
                if not target.startswith(('http://', 'https://')):
                    domain_ip = extract_relevant_target(target)
                    is_ip = all(c.isdigit() or c == '.' for c in domain_ip)
                    target_url = f"https://{domain_ip}" if is_ip else f"http://{domain_ip}"

                # Replace placeholders
                cmd = [part.replace("{target_ip_or_domain}", target_for_cmd)
                            .replace("{target_with_port}", target_for_cmd)
                            .replace("{target}", target_for_cmd)
                            .replace("{target_url}", target_url)
                            for part in cmd_template]

                # OPTIMIZACIÓN: Verificar si el comando tiene /path/to/ estándar que aún no ha sido actualizado
                has_dummy_path = any("/path/to/" in part for part in cmd)
                if has_dummy_path:
                    path_error = f"Error: El comando para {display_name} contiene una ruta de directorio genérica (/path/to/). Por favor configure la herramienta correctamente."
                    send_event("log", {"message": path_error, "level": "error"})
                    send_event("tool_error", {"tool_name": tool_name, "error": path_error})
                    tool_final_status = "error"
                    send_event("tool_complete", {"tool_name": tool_name, "status": tool_final_status})
                    return tool_name, path_error

                # OPTIMIZACIÓN: Fix específico para theHarvester
                if tool_name == "theHarvester" and "-b google,bing,linkedin" in cmd_str:
                    # Reemplazar con solo bing ya que los otros pueden causar errores
                    cmd = [part.replace("google,bing,linkedin", "bing") for part in cmd]
                    send_event("log", {"message": "Usando solo motor de búsqueda 'bing' para theHarvester para evitar errores", "level": "info"})
                
                logging.info(f"Task {task_id}: Prepared command: {' '.join(cmd)}")

                # OPTIMIZACIÓN ADICIONAL: Establecer límite de tamaño para la salida
                max_output_size = 1024 * 100  # 100KB máximo para la salida de cada herramienta
                total_output_size = 0

                # Stream the tool execution
                for stream_event in ToolRunner.run_stream(cmd, timeout=timeout):
                    event_type = stream_event.get("event")
                    event_data = stream_event.get("data")

                    # Check again if stopped during tool execution
                    if scan_tasks.get(task_id, {}).get('status') == 'stopped':
                        logging.info(f"Task {task_id}: Stopping tool {display_name} mid-execution.")
                        # Attempt to kill process? ToolRunner might need enhancement for this
                        # For now, just stop processing output
                        raise StopIteration # Exit the inner loop gracefully

                    if event_type == "result_chunk":
                        # OPTIMIZACIÓN: Verificar tamaño de salida
                        if event_data and isinstance(event_data, str):
                            chunk_size = len(event_data.encode('utf-8', errors='replace'))
                            total_output_size += chunk_size
                            
                            # Si excede el límite, truncar
                            if total_output_size > max_output_size:
                                truncate_msg = f"\n\n[...] Salida truncada por exceder el límite de tamaño ({max_output_size/1024:.1f}KB)\n"
                                send_event("result_chunk", {"tool_name": tool_name, "chunk": truncate_msg})
                                send_event("log", {"message": f"Salida de {display_name} truncada para evitar sobrecarga", "level": "warning"})
                                break
                        
                        # OPTIMIZACIÓN: Eliminar líneas de error comunes conocidas o convertirlas en más amigables
                        if "Command 'trufflehog github --org" in event_data or "Command 'sherlock" in event_data:
                            clean_data = "Se requiere instalación adicional para esta herramienta."
                            event_data = clean_data
                        
                        tool_output_chunks.append(event_data)
                        send_event("result_chunk", {"tool_name": tool_name, "chunk": event_data})
                    elif event_type == "log":
                        send_event("log", {"message": event_data, "level": "info"})
                    elif event_type == "tool_error":
                        send_event("tool_error", {"tool_name": tool_name, "error": event_data})
                        tool_final_status = "error"
                    elif event_type == "tool_complete":
                        # ToolRunner signals completion, we send our event later after accumulating output
                        pass

            except StopIteration:
                # Handle graceful stop during tool run
                tool_final_status = "stopped"
                log_msg = f"Herramienta {display_name} detenida por el usuario."
                send_event("log", {"message": log_msg, "level": "warning"})
            except Exception as runner_e:
                error_msg = f"Error ejecutando {display_name}: {runner_e}"
                logging.exception(f"Task {task_id}: Tool execution error")
                tool_output_chunks.append(error_msg)
                send_event("log", {"message": error_msg, "level": "error"})
                send_event("tool_error", {"tool_name": tool_name, "error": error_msg})
                tool_final_status = "error"

            # Send tool completion event after loop/error handling
            send_event("tool_complete", {"tool_name": tool_name, "status": tool_final_status})
            if tool_final_status != 'stopped': # Avoid redundant log if stopped
                log_lvl = "warning" if tool_final_status == 'error' else "success"
                send_event("log", {"message": f"{display_name} finalizado con estado: {tool_final_status}.", "level": log_lvl})

        # Actualizar el progreso después de terminar cada herramienta
        completed_tools_in_phase += 1
        current_progress = int(base_progress + (completed_tools_in_phase * progress_per_tool))
        # Prevent progress going backwards if stop signal comes late
        if task.get('status') != 'stopped':
            task['progress'] = max(task.get('progress', 0), current_progress)
            send_event("progress", {"progress": task['progress']})
        task['current_tool'] = None # Clear current tool after completion

        # Store accumulated results for this tool
        return tool_name, "".join(tool_output_chunks)

    # OPTIMIZACIÓN: Organizar herramientas por prioridad y rapidez
    # Primero ejecutaremos las herramientas rápidas, luego las más lentas
    fast_tools = []
    slow_tools = []
    
    # Clasificar herramientas
    for tool_name in tools:
        tool_info = tool_definitions.get(tool_name, {})
        # Herramientas que generalmente son más rápidas
        if tool_name in ['whois', 'nslookup', 'dig', 'host', 'wafw00f', 'ssl_scan']:
            fast_tools.append(tool_name)
        # Herramientas que normalmente son más lentas
        elif tool_name in ['sublist3r', 'nmap_quick', 'nmap_full', 'theHarvester', 'nikto']:
            slow_tools.append(tool_name)
        # Para el resto, usar el timeout como indicador de velocidad
        elif tool_info.get('timeout', 300) <= 300:
            fast_tools.append(tool_name)
        else:
            slow_tools.append(tool_name)
    
    # Crear una lista ordenada con herramientas rápidas primero
    ordered_tools = fast_tools + slow_tools
    
    # Ejecutar las herramientas de forma escalonada usando ThreadPoolExecutor
    tool_results = {}
    
    try:
        # OPTIMIZACIÓN: Ajustar workers según la fase
        # REDUCIR más los workers para evitar bloqueos
        max_workers = 2 if is_osint_phase else 1
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Lanzar todas las tareas de herramientas en el orden optimizado
            future_to_tool = {
                executor.submit(run_single_tool, tool_name): tool_name 
                for tool_name in ordered_tools
            }
            
            # OPTIMIZACIÓN: Implementar tiempo de espera global
            future_wait_timeout = 900  # 15 minutos máximo para todas las tareas
            
            try:
                # Recoger resultados con tiempo límite global
                for future in concurrent.futures.as_completed(future_to_tool, timeout=future_wait_timeout):
                    tool_name = future_to_tool[future]
                    try:
                        output = future.result(timeout=60)  # 60 segundos máximo para obtener el resultado
                        if output is not None and isinstance(output, tuple) and len(output) == 2:
                            tool_name, tool_output = output
                            if tool_name is not None:  # Ignorar herramientas que fallan en la verificación inicial
                                tool_results[tool_name] = tool_output
                    except concurrent.futures.TimeoutError:
                        error_msg = f"Tiempo de espera agotado al obtener resultados de {tool_name}"
                        logging.warning(f"Task {task_id}: {error_msg}")
                        send_event("log", {"message": error_msg, "level": "warning"})
                        # Intentar cancelar la tarea
                        future.cancel()
                    
                    # Si se solicitó detener, cancelamos las tareas pendientes
                    if task.get('status') == 'stopped':
                        logging.info(f"Task {task_id}: Cancelling remaining tools in ThreadPool")
                        for f in future_to_tool:
                            if not f.done():
                                f.cancel()
                        break
                    
            except concurrent.futures.TimeoutError:
                error_msg = f"Tiempo máximo global ({future_wait_timeout} segundos) excedido para la fase {current_phase_name}"
                logging.warning(f"Task {task_id}: {error_msg}")
                send_event("log", {"message": error_msg, "level": "warning"})
                # Cancelar todas las tareas pendientes
                for f in future_to_tool:
                    if not f.done():
                        f.cancel()
    
    except Exception as e:
        error_msg = f"Error en la ejecución de herramientas: {str(e)}"
        logging.exception(f"Task {task_id}: Thread pool error")
        send_event("log", {"message": error_msg, "level": "error"})
    
    # Almacenar resultados finales
    task[result_key] = tool_results

    # Si no fue detenido, indicar finalización de fase
    if task.get('status') != 'stopped':
        log_msg = f"Fase {current_phase_name} completada."
        send_event("log", {"message": log_msg, "level": "success"})
        logging.info(f"Task {task_id}: Phase {result_key} completed.")

    # Check if next phase needs to run or if scan is complete
    is_last_phase = (not is_osint_phase or not task.get('vuln_tools'))

    if not is_last_phase and task.get('status') != 'stopped':
        # OPTIMIZACIÓN: Añadir un retraso entre fases para permitir que el sistema se recupere
        time.sleep(3)
        
        # Start the next phase (Vuln scan)
        logging.info(f"Task {task_id}: OSINT phase done, starting Vuln phase.")
        # Ensure the next thread uses the correct arguments
        next_tools = task.get('vuln_tools', [])
        if next_tools:
            # OPTIMIZACIÓN: Limitar el número de herramientas de vulnerabilidad
            if len(next_tools) > 5:  # Vulnerabilidad es más intensiva, limitamos a 5
                next_tools = next_tools[:5]
                log_msg = f"⚠️ Limitando a 5 herramientas de vulnerabilidad para evitar sobrecarga"
                send_event("log", {"message": log_msg, "level": "warning"})
                logging.warning(f"Task {task_id}: Limiting to 5 vulnerability tools to prevent overload")
            
            thread_vuln = threading.Thread(target=run_scan_phase, args=(task_id, target, next_tools, VULN_TOOLS, 'results_vuln'))
            task['thread_vuln'] = thread_vuln
            thread_vuln.start()
        else:
            # Should not happen if has_both_phases was true, but handle defensively
            logging.warning(f"Task {task_id}: Tried to start vuln phase, but no vuln tools selected?")
            is_last_phase = True # Treat as last phase now

    if is_last_phase and task.get('status') != 'stopped':
        # This was the last phase or the next phase couldn't start
        task['status'] = 'completed'
        task['progress'] = 100
        # Establecer el tiempo de fin
        task['end_time'] = datetime.now()
        log_msg = "Todos los escaneos completados."
        send_event("status", {"status": "completed", "progress": 100, "message": log_msg})
        send_event("log", {"message": log_msg, "level": "success"})
        logging.info(f"Scan task {task_id} fully completed.")
        task_queue.put(None) # Signal the SSE stream to close
    elif task.get('status') == 'stopped':
        # Establecer el tiempo de fin en caso de detención
        task['end_time'] = datetime.now()
        # Ensure SSE stream closes if stopped after phase completion but before next phase start
        logging.info(f"Task {task_id} stopped, signaling SSE stream closure after phase {result_key}.")
        task_queue.put(None)

@app.route('/scan_status/<task_id>')
def scan_status(task_id):
    """Returns the current known status and progress (less critical now with SSE)."""
    task = scan_tasks.get(task_id)
    if not task:
        return jsonify({"error": "Tarea no encontrada"}), 404

    # Primarily used for fetching final state or if SSE disconnects
    return jsonify({
        "status": task.get('status', 'unknown'),
        "progress": task.get('progress', 0),
        "current_tool": task.get('current_tool'), # Might be useful
    })

@app.route('/scan_results/<task_id>')
def scan_results(task_id):
    """Returns the final accumulated results (used for report gen)."""
    task = scan_tasks.get(task_id)
    if not task:
        return jsonify({"error": "Tarea no encontrada"}), 404
    # Allow fetching results even if stopped, just maybe not queued/running
    if task['status'] in ['queued', 'running_results_osint', 'running_results_vuln']:
         return jsonify({"error": "El escaneo aún no ha finalizado o sido detenido para obtener resultados finales consolidados."}), 400

    return jsonify({
        "osint_results": task.get('results_osint', {}),
        "vuln_results": task.get('results_vuln', {})
    })

@app.route('/results/<task_id>')
def results_view(task_id):
    """View results of a scan."""
    if task_id not in tasks:
        return jsonify({"error": "Task not found"}), 404
    
    task_data = tasks[task_id]
    
    if task_data['status'] == 'running':
        return render_template('running.html', task_id=task_id, target=task_data['target'])
    elif task_data['status'] == 'completed':
        return render_template(
            'results.html', 
            task_id=task_id, 
            target=task_data['target'], 
            results=task_data['results'],
            tools_used=task_data['tools']
        )
    else:
        return jsonify({"error": "Invalid task status"}), 500

# Ruta para generar reporte HTML mejorado
@app.route('/report/<task_id>')
def html_report(task_id):
    tasks = load_tasks()
    task_info = next((t for t in tasks if t['id'] == task_id), None)
    
    if not task_info:
        flash('Tarea no encontrada', 'danger')
        return redirect(url_for('index'))
    
    # Separar resultados en OSINT y vulnerabilidades
    osint_results = {}
    vuln_results = {}
    
    for tool_id in task_info.get('selected_tools', []):
        tool_output_file = os.path.join('reports', f"{task_id}_{tool_id}.txt")
        if os.path.exists(tool_output_file):
            try:
                with open(tool_output_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    
                    # Clasificar herramienta como OSINT o Vulnerabilidad
                    is_osint = any(tool['id'] == tool_id for tool in OSINT_TOOLS)
                    is_vuln = any(tool['id'] == tool_id for tool in VULN_TOOLS)
                    
                    if is_osint:
                        osint_results[tool_id] = content
                    elif is_vuln:
                        vuln_results[tool_id] = content
            except Exception as e:
                logging.error(f"Error al leer el archivo de resultados {tool_output_file}: {str(e)}")
    
    # Obtener nombres de herramientas
    tool_names = {}
    for tool in OSINT_TOOLS + VULN_TOOLS:
        tool_names[tool['id']] = tool['name']
    
    # Calcular métricas para el reporte
    total_tools = len(task_info.get('selected_tools', []))
    osint_count = len(osint_results)
    vuln_count = len(vuln_results)
    
    # Calcular duración del análisis
    start_time = task_info.get('start_time')
    end_time = task_info.get('end_time')
    scan_duration = "Desconocido"
    
    if start_time and end_time:
        start = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
        end = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
        duration = end - start
        scan_duration = f"{duration.seconds // 60} minutos {duration.seconds % 60} segundos"
    
    # Detección heurística de hallazgos críticos (simplificado)
    hallazgos_criticos = 0
    keywords_criticos = [
        "vulnerable", "critical", "high severity", "exploitable", 
        "exposed", "weak password", "outdated", "puerto 22", "puerto 3389",
        "error", "failure", "fallo", "vulnerabilidad", "crítico", "obsoleto"
    ]
    
    for resultado in list(osint_results.values()) + list(vuln_results.values()):
        for keyword in keywords_criticos:
            if keyword.lower() in resultado.lower():
                hallazgos_criticos += 1
                break
    
    return render_template('report.html',
                          target=task_info.get('target', 'Desconocido'),
                          timestamp=datetime.now().strftime("%d-%m-%Y %H:%M:%S"),
                          task_id=task_id,
                          scan_duration=scan_duration,
                          total_tools=total_tools,
                          osint_count=osint_count,
                          vuln_count=vuln_count,
                          hallazgos_criticos=hallazgos_criticos,
                          osint_results=osint_results,
                          vuln_results=vuln_results,
                          tool_names=tool_names)

# Ruta para exportar reporte HTML
@app.route('/export_html_report/<task_id>')
def export_html_report(task_id):
    tasks = load_tasks()
    task_info = next((t for t in tasks if t['id'] == task_id), None)
    
    if not task_info:
        flash('Tarea no encontrada', 'danger')
        return redirect(url_for('index'))
    
    # Generar nombre de archivo único para el reporte
    target = safe_filename(task_info.get('target', 'target'))
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    filename = f"OSINTdesk_Reporte_{target}_{timestamp}.html"
    
    # Crear una versión estática del reporte HTML
    html_content = html_report(task_id)
    
    # Configurar la respuesta para descargar el archivo
    response = make_response(html_content)
    response.headers["Content-Disposition"] = f"attachment; filename={filename}"
    response.headers["Content-Type"] = "text/html"
    
    return response

@app.route('/stop_scan/<task_id>', methods=['POST'])
def stop_scan(task_id):
    task = scan_tasks.get(task_id)
    if not task:
        return jsonify({"error": "Tarea no encontrada"}), 404

    current_status = task.get('status', 'unknown')
    if current_status not in ['queued', 'running_results_osint', 'running_results_vuln']:
         return jsonify({"message": "El escaneo no está en ejecución o ya finalizó."}), 400

    logging.warning(f"Stop request received for task {task_id}")
    task['status'] = 'stopped' # Signal threads to stop

    # Send stop signal via SSE queue
    if task_id in clients:
        clients[task_id].put({"event": "status", "data": {"status": "stopped", "progress": task['progress'], "message": "Solicitud de parada recibida."}})
        clients[task_id].put({"event": "log", "data": {"message": "*** Solicitud de parada recibida ***", "level": "warning"}})
        # Signal stream to close eventually after thread stops checking
        clients[task_id].put(None)

    # Note: Stopping external processes is still hard. This stops the Python loop.
    return jsonify({"message": "Solicitud de parada enviada."}) 

@app.route('/generate_report/<task_id>')
def generate_report_route(task_id):
    task = scan_tasks.get(task_id)
    if not task:
        return "Tarea no encontrada", 404
    if task['status'] != 'completed' and task['status'] != 'stopped':
         return "El escaneo debe finalizar antes de generar el reporte", 400

    target = task['target']
    report_title = request.args.get('report_title', "Reporte de Análisis de Seguridad")
    company_name = request.args.get('company_name', "")
    include_summary = request.args.get('include_summary', 'true').lower() == 'true'
    include_osint = request.args.get('include_osint', 'true').lower() == 'true'
    include_vuln = request.args.get('include_vuln', 'true').lower() == 'true'
    include_viz = request.args.get('include_viz', 'true').lower() == 'true'
    
    results = {"osint": task['results_osint'], "vuln": task['results_vuln']}
    
    report_filename = f"reporte_{target.replace('://', '_').replace('/', '_')}_{task_id[:8]}.pdf"
    chart_filename = f"chart_{task_id[:8]}.png"
    report_path = os.path.join("temp", report_filename) # Use a temp dir
    chart_path = os.path.join("temp", chart_filename)
    os.makedirs("temp", exist_ok=True)

    try:
        pdf = ReportPDF(company_name=company_name, report_title=report_title)
        pdf.add_title_page(target, datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        if include_summary:
            pdf.add_summary(target, datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 
                            list(results["osint"].keys()) + list(results["vuln"].keys()), 
                            results)

        if include_viz:
            try:
                create_visualization_image(results, chart_path)
                pdf.add_chart(chart_path)
            except Exception as e:
                 logging.error(f"Error creating/adding chart for task {task_id}: {e}")

        if include_osint and results["osint"]:
            for tool, output in results["osint"].items():
                display_name = OSINT_TOOLS.get(tool, {}).get('display', tool.capitalize())
                pdf.add_section(f"OSINT: {display_name}", output)
        
        if include_vuln and results["vuln"]:
            for tool, output in results["vuln"].items():
                display_name = VULN_TOOLS.get(tool, {}).get('display', tool.capitalize())
                pdf.add_section(f"Vulnerabilidad: {display_name}", output)

        pdf.output(report_path)
        logging.info(f"Report generated for task {task_id} at {report_path}")

        return send_file(report_path, as_attachment=True, download_name=report_filename)

    except Exception as e:
        logging.exception(f"Failed to generate report for task {task_id}")
        return f"Error generando reporte: {e}", 500
    finally:
        # Clean up temp files
        if os.path.exists(chart_path):
            try: os.remove(chart_path)
            except Exception as e: logging.warning(f"Could not remove temp chart {chart_path}: {e}")
        # Don't remove the PDF immediately, let send_file handle it or clean up later
        # if os.path.exists(report_path):
        #    try: os.remove(report_path)
        #    except Exception as e: logging.warning(f"Could not remove temp report {report_path}: {e}")

def create_visualization_image(results, output_path):
    # Logic adapted from OsintVulnToolkit.create_visualization
    figure = plt.figure(figsize=(10, 5), dpi=100)
    osint_tools_run_count = len(results.get("osint", {}))
    vuln_tools_run_count = len(results.get("vuln", {}))
    
    # Placeholder severity counts
    simulated_vuln_counts = {'Critical': 0, 'High': 0, 'Medium': 0, 'Low': 0, 'Info': 0}
    if results.get("vuln"): 
         simulated_vuln_counts['Critical'] += 1; simulated_vuln_counts['High'] += 3
         simulated_vuln_counts['Medium'] += 5; simulated_vuln_counts['Low'] += 9; simulated_vuln_counts['Info'] += 12
    has_vuln_data = any(simulated_vuln_counts.values())

    if not has_vuln_data and (osint_tools_run_count + vuln_tools_run_count == 0):
        ax = figure.add_subplot(111)
        ax.text(0.5, 0.5, 'Sin resultados para visualizar.', ha='center', va='center')
        ax.axis('off')
    elif not has_vuln_data:
        ax1 = figure.add_subplot(111)
        labels = ['Herramientas OSINT', 'Herramientas Vuln.']
        sizes = [osint_tools_run_count, vuln_tools_run_count]
        colors = ['#6baed6', '#fc8d62']
        explode = (0, 0.05) if vuln_tools_run_count > 0 else (0, 0)
        ax1.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90, colors=colors, explode=explode)
        ax1.axis('equal')
        ax1.set_title("Distribución de Herramientas Ejecutadas")
    else:
        ax1 = figure.add_subplot(121)
        labels = ['OSINT', 'Vuln.']
        sizes = [osint_tools_run_count, vuln_tools_run_count]
        colors = ['#6baed6', '#fc8d62']
        explode = (0, 0.05)
        ax1.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90, colors=colors, explode=explode)
        ax1.axis('equal')
        ax1.set_title("Herramientas Ejecutadas")

        ax2 = figure.add_subplot(122)
        categories = ['Crítico', 'Alto', 'Medio', 'Bajo', 'Info']
        values = [simulated_vuln_counts.get(cat_en, 0) for cat_en in ['Critical', 'High', 'Medium', 'Low', 'Info']]
        color_map = {'Crítico': '#d7191c', 'Alto': '#fdae61', 'Medio': '#ffffbf', 'Bajo': '#abd9e9', 'Info': '#2c7bb6'}
        bar_colors = [color_map.get(cat, 'gray') for cat in categories]
        bars = ax2.bar(categories, values, color=bar_colors, edgecolor='grey')
        ax2.set_title("Hallazgos por Severidad (Simulado)")
        ax2.set_ylabel("Número")
        ax2.tick_params(axis='x', rotation=30, ha='right')
        ax2.spines['top'].set_visible(False); ax2.spines['right'].set_visible(False)
        ax2.bar_label(bars, padding=3)

    figure.tight_layout(pad=2.0)
    figure.savefig(output_path, dpi=150)
    plt.close(figure) # Close figure to free memory

# --- SSE Stream Route ---
@app.route('/stream/<task_id>')
def stream(task_id):
    """Server-Sent Events stream for a specific task."""
    task_queue = clients.get(task_id)
    target = request.args.get('target', '')
    
    if not task_queue:
        logging.warning(f"SSE stream requested for unknown or finished task: {task_id}")
        return render_template('error.html', error_message="Tarea de análisis no encontrada o finalizada")

    task = scan_tasks.get(task_id, {})
    # Calcular la cantidad total de herramientas seleccionadas
    total_tools = len(task.get('osint_tools', [])) + len(task.get('vuln_tools', []))
    
    # Renderizar la plantilla con server-sent events
    return render_template('stream.html', task_id=task_id, target=target, total_tools=total_tools)

@app.route('/stream_events/<task_id>')
def stream_events(task_id):
    """Server-Sent Events endpoint for real-time updates."""
    task_queue = clients.get(task_id)
    
    if not task_queue:
        logging.warning(f"SSE stream requested for unknown or finished task: {task_id}")
        def immediate_close():
            status = scan_tasks.get(task_id, {}).get('status', 'unknown')
            message = json.dumps({'status': status, 'message': 'Task not actively streaming.'})
            # Correct SSE format
            yield f"event: scan_complete\ndata: {message}\n\n"
        return Response(immediate_close(), mimetype='text/event-stream')

    def event_stream():
        # Send connection confirmation
        conn_msg = json.dumps({'message': 'SSE stream connected'})
        yield f"event: connected\ndata: {conn_msg}\n\n"

        keep_running = True
        while keep_running:
            try:
                message = task_queue.get(timeout=60) # Wait for a message

                if message is None: # Sentinel value to close stream
                    logging.info(f"SSE stream closing for task {task_id} (received None signal)")
                    keep_running = False
                    # Send a final completion event before breaking
                    final_status = scan_tasks.get(task_id, {}).get('status', 'completed')
                    final_msg = json.dumps({'status': final_status})
                    yield f"event: scan_complete\ndata: {final_msg}\n\n"
                    break # Exit the while loop

                # Format and yield the message
                if isinstance(message, dict):
                    event_type = message.get('event', 'message')
                    # Ensure data is always a JSON string payload
                    data_payload = json.dumps(message.get('data', ''))
                    sse_message = f"event: {event_type}\ndata: {data_payload}\n\n"
                    yield sse_message
                else:
                    # Fallback for non-dict messages (treat as log)
                    log_payload = json.dumps(str(message))
                    yield f"event: log\ndata: {log_payload}\n\n"

                task_queue.task_done()

            except queue.Empty:
                # Timeout waiting for message, send keep-alive comment
                yield ": keep-alive\n\n"
            except Exception as e:
                logging.error(f"Error in SSE stream for task {task_id}: {e}")
                keep_running = False # Stop on error
                # Attempt to send an error event to the client
                try:
                    error_payload = json.dumps({'error': 'Internal stream error'})
                    yield f"event: stream_error\ndata: {error_payload}\n\n"
                except Exception as e_report:
                    logging.error(f"Failed to send stream_error event: {e_report}")
                break # Exit loop after error

        logging.info(f"SSE event_stream loop finished for task {task_id}")

    # Set headers for SSE
    response = Response(event_stream(), mimetype='text/event-stream')
    response.headers['Cache-Control'] = 'no-cache'
    response.headers['X-Accel-Buffering'] = 'no' # Useful for Nginx buffering issues
    return response

@app.route('/export_report/<task_id>')
def export_report(task_id):
    """Exporta el reporte HTML como un archivo descargable."""
    task = scan_tasks.get(task_id)
    if not task:
        return render_template('error.html', error_message="Tarea de análisis no encontrada"), 404
    
    target = task.get('target', 'desconocido')
    safe_target = ''.join(c if c.isalnum() else '_' for c in target)
    
    # Obtener el contenido HTML del reporte
    html_content = html_report(task_id)
    
    # Crear el nombre del archivo
    filename = f"OSINTdesk_Reporte_{safe_target}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
    
    # Devolver el contenido como archivo descargable
    return Response(
        html_content, 
        mimetype='text/html',
        headers={
            'Content-Disposition': f'attachment; filename="{filename}"',
            'Content-Type': 'text/html; charset=utf-8'
        }
    )

# Add new routes for HTML report generation
@app.route('/download_html_report/<task_id>')
def download_html_report(task_id):
    """Download the HTML report as a file."""
    task = scan_tasks.get(task_id)
    if not task:
        return "Tarea no encontrada", 404
    
    # Generate the HTML content
    html_content = generate_html_report(task_id)
    if isinstance(html_content, tuple):  # Error response
        return html_content
    
    # Create filename based on target and timestamp
    target = task['target']
    safe_target = ''.join(c if c.isalnum() else '_' for c in target)
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"OSINTdesk_Report_{safe_target}_{timestamp}.html"
    
    # Create response for file download
    response = make_response(html_content)
    response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'
    response.headers['Content-Type'] = 'text/html; charset=utf-8'
    
    return response

# Also add a link to the report on the results page
@app.route('/results/<task_id>')
def results(task_id):
    """View results of a scan."""
    if task_id not in tasks:
        return jsonify({"error": "Task not found"}), 404
    
    task_data = tasks[task_id]
    
    if task_data['status'] == 'running':
        return render_template('running.html', task_id=task_id, target=task_data['target'])
    elif task_data['status'] == 'completed':
        return render_template(
            'results.html', 
            task_id=task_id, 
            target=task_data['target'], 
            results=task_data['results'],
            tools_used=task_data['tools']
        )
    else:
        return jsonify({"error": "Invalid task status"}), 500

    vulnerabilities = []
    if 'vuln_results' in task and 'nikto' in task['vuln_results']:
        nikto_output = task['vuln_results']['nikto']
        for line in nikto_output.splitlines():
            if '+ ' in line and ': ' in line:
                parts = line.split(': ', 1)
                if len(parts) == 2:
                    vuln_id = parts[0].strip()
                    vuln_desc = parts[1].strip()
                    
                    # Determinar severidad basada en palabras clave (simplificado)
                    severity = 'low'
                    if any(keyword in vuln_desc.lower() for keyword in ['critical', 'high', 'severe']):
                        severity = 'high'
                    elif any(keyword in vuln_desc.lower() for keyword in ['medium', 'moderate']):
                        severity = 'medium'
                    
                    vulnerabilities.append({
                        'title': f'Nikto: {vuln_id}',
                        'description': vuln_desc,
                        'severity': severity,
                        'solution': 'Se recomienda verificar y parchear la vulnerabilidad',
                        'references': 'https://cirt.net/Nikto2'
                    })
    
    results['vulnerabilities'] = vulnerabilities
    
    # Generar métricas para el informe
    metrics = {
        'domains': len(subdomains),
        'ips': random.randint(1, 10),  # Ejemplo simplificado
        'emails': len(emails),
        'technologies': len(technologies),
        'vulnerabilities': len(vulnerabilities)
    }
    
    # Generar imagen de visualización si hay resultados suficientes
    visualization_path = None
    if any(metric > 0 for metric in metrics.values()):
        visualization_path = os.path.join('osintdesk', 'temp', f'visualization_{task_id}.png')
        create_visualization_image(results, visualization_path)
    
    # Renderizar informe HTML
    timestamp = time.strftime('%d/%m/%Y %H:%M:%S')
    return render_template('html_report.html', 
                          target=target, 
                          results=results, 
                          metrics=metrics, 
                          timestamp=timestamp,
                          visualization_path=visualization_path)

# Ruta para exportar el informe HTML
@app.route('/generate_report', methods=['POST'])
def generate_report_from_form():
    target = request.form.get('target', '')
    selected_tools = request.form.getlist('tools')
    
    if not target or not selected_tools:
        flash('Por favor, ingrese un objetivo y seleccione al menos una herramienta', 'warning')
        return redirect(url_for('index'))
    
    # Collect results from various tools
    results = {}
    metrics = {
        'subdomains': 0,
        'emails': 0,
        'technologies': 0,
        'vulnerabilities': 0
    }
    
    # Example data - in a real implementation, this would come from actual tool outputs
    subdomains = []
    emails = []
    technologies = []
    vulnerabilities = []
    tool_results = []
    whois_info = {}
    
    # Run selected tools and gather their results
    for tool in selected_tools:
        tool_output = run_tool(tool, target)
        
        # Process results based on tool type
        if tool == 'whois':
            whois_info = parse_whois_output(tool_output)
        elif tool == 'sublist3r':
            parsed_domains = parse_sublist3r_output(tool_output)
            subdomains.extend(parsed_domains)
            metrics['subdomains'] += len(parsed_domains)
        elif tool == 'theHarvester':
            parsed_emails = parse_harvester_output(tool_output)
            emails.extend(parsed_emails)
            metrics['emails'] += len(parsed_emails)
        elif tool == 'wappalyzer':
            parsed_techs = parse_wappalyzer_output(tool_output)
            technologies.extend(parsed_techs)
            metrics['technologies'] += len(parsed_techs)
        elif tool in ['nikto', 'sqlmap', 'wpscan']:
            parsed_vulns = parse_vuln_tool_output(tool, tool_output)
            vulnerabilities.extend(parsed_vulns)
            metrics['vulnerabilities'] += len(parsed_vulns)
        
        # Add to tool results list
        tool_type = 'vulnerability' if tool in ['nikto', 'sqlmap', 'wpscan'] else 'osint'
        tool_results.append({
            'name': tool.capitalize(),
            'type': tool_type,
            'description': get_tool_description(tool),
            'output': tool_output
        })
    
    # Generate date string
    current_date = datetime.now().strftime("%d de %B de %Y, %H:%M")
    
    # Render the HTML report template with the results
    return render_template(
        'html_report.html',
        target=target,
        date=current_date,
        metrics=metrics,
        subdomains=subdomains,
        emails=emails,
        technologies=technologies,
        vulnerabilities=vulnerabilities,
        tool_results=tool_results,
        whois_info=whois_info
    )

# Helper functions for parsing tool outputs
def parse_whois_output(output):
    # In real implementation, parse the WHOIS output
    # This is just a placeholder example
    return {
        'registrant': 'Example Registrant',
        'organization': 'Example Org',
        'creation_date': '2020-01-01',
        'expiration_date': '2025-01-01',
        'registrar': 'Example Registrar',
        'country': 'US',
        'name_servers': ['ns1.example.com', 'ns2.example.com'],
        'status': 'Active'
    }

def parse_sublist3r_output(output):
    # Parse Sublist3r output to extract subdomains
    # This is a simplified example
    domains = []
    for line in output.strip().split('\n'):
        if line and not line.startswith('[') and '.' in line:
            domains.append({
                'name': line.strip(),
                'ip': get_ip_for_domain(line.strip()),
                'status': 'active'
            })
    return domains

def get_ip_for_domain(domain):
    # In a real implementation, perform DNS lookup
    # Placeholder for example
    return "***********"

def parse_harvester_output(output):
    # Parse theHarvester output to extract emails
    # Simplified example
    emails = []
    for line in output.strip().split('\n'):
        if '@' in line:
            email_part = line.strip()
            if '<' in email_part and '>' in email_part:
                email_part = email_part.split('<')[1].split('>')[0]
            emails.append({
                'address': email_part,
                'source': 'theHarvester'
            })
    return emails

def parse_wappalyzer_output(output):
    # Parse Wappalyzer output to extract technologies
    # Simplified example
    technologies = []
    try:
        tech_data = json.loads(output)
        for tech in tech_data.get('technologies', []):
            technologies.append({
                'name': tech.get('name', 'Unknown'),
                'version': tech.get('version', ''),
                'category': tech.get('categories', [{}])[0].get('name', 'Other')
            })
    except:
        pass
    return technologies

def parse_vuln_tool_output(tool, output):
    # Parse vulnerability tool output
    # Simplified example
    vulnerabilities = []
    if tool == 'nikto':
        for line in output.strip().split('\n'):
            if 'OSVDB' in line or 'vulnerability' in line.lower():
                vulnerabilities.append({
                    'name': line[:50] + '...' if len(line) > 50 else line,
                    'severity': determine_severity(line),
                    'path': '/',
                    'details': line
                })
    elif tool == 'sqlmap':
        for line in output.strip().split('\n'):
            if 'vulnerable' in line.lower():
                vulnerabilities.append({
                    'name': 'SQL Injection',
                    'severity': 'high',
                    'path': extract_path_from_sqlmap(line),
                    'details': line
                })
    return vulnerabilities

def determine_severity(text):
    # Simple logic to determine severity based on text
    text = text.lower()
    if any(word in text for word in ['critical', 'high', 'rce', 'sql injection']):
        return 'high'
    elif any(word in text for word in ['medium', 'xss', 'csrf']):
        return 'medium'
    else:
        return 'low'

def extract_path_from_sqlmap(line):
    # Extract path from sqlmap output - simplified example
    return '/'

def get_tool_description(tool):
    # Return description for each tool
    descriptions = {
        'whois': 'Obtiene información de registro del dominio incluyendo propietario, fechas y servidores DNS.',
        'sublist3r': 'Herramienta para descubrir subdominios utilizando múltiples fuentes.',
        'theHarvester': 'Busca direcciones de correo electrónico, nombres, subdominios y mucho más.',
        'wappalyzer': 'Identifica tecnologías utilizadas en sitios web.',
        'nikto': 'Escáner de vulnerabilidades web que realiza pruebas exhaustivas contra servidores web.',
        'sqlmap': 'Herramienta automática de detección y explotación de vulnerabilidades SQL injection.',
        'wpscan': 'Escáner de seguridad específico para sitios WordPress.'
    }
    return descriptions.get(tool, 'Herramienta de análisis de seguridad')

def run_tool(tool, target):
    # In a real implementation, this would execute the tool
    # For this example, return placeholder text
    return f"Ejemplo de salida para la herramienta {tool} en el objetivo {target}\n"

@app.route('/html_report/<task_id>')
def generate_html_report(task_id):
    """Generate and display an HTML report for a completed scan."""
    task = scan_tasks.get(task_id)
    if not task:
        return "Tarea no encontrada", 404
    if task['status'] != 'completed' and task['status'] != 'stopped':
        return "El escaneo debe finalizar antes de generar el reporte", 400
    
    target = task['target']
    timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # Get scan results
    osint_results = task.get('results_osint', {})
    vuln_results = task.get('results_vuln', {})
    
    # Calculate metrics for the report
    metrics = {
        'domains': random.randint(5, 15),  # Simulated values for demonstration
        'ips': random.randint(2, 8),
        'emails': random.randint(3, 12),
        'technologies': random.randint(4, 10),
        'vulnerabilities': random.randint(0, 7)
    }
    
    # Process results for the expected template format
    results = {
        'osint_results': osint_results,
        'vuln_results': vuln_results
    }
    
    # Simulate some additional data for the demonstration
    results['subdomains'] = []
    for i in range(metrics['domains']):
        results['subdomains'].append({
            'name': f"sub{i}.{target}" if not target.startswith(('http://', 'https://')) else f"sub{i}.{target.split('//')[1]}",
            'ip': f"192.168.1.{random.randint(1, 254)}"
        })
    
    results['emails'] = []
    for i in range(metrics['emails']):
        domain = target if not target.startswith(('http://', 'https://')) else target.split('//')[1]
        domain = domain.split('/')[0]  # Remove any path
        results['emails'].append({
            'address': f"user{i}@{domain}",
            'source': 'Email Harvester'
        })
    
    results['technologies'] = []
    tech_names = ['Apache', 'Nginx', 'PHP', 'MySQL', 'WordPress', 'jQuery', 'Bootstrap', 'React', 'Node.js', 'MongoDB']
    tech_categories = ['Web Server', 'Web Server', 'Programming Language', 'Database', 'CMS', 'JavaScript Library', 
                      'CSS Framework', 'JavaScript Framework', 'JavaScript Runtime', 'Database']
    for i in range(metrics['technologies']):
        results['technologies'].append({
            'name': tech_names[i % len(tech_names)],
            'version': f"{random.randint(1, 9)}.{random.randint(0, 9)}.{random.randint(0, 99)}",
            'category': tech_categories[i % len(tech_categories)]
        })
    
    results['vulnerabilities'] = []
    vuln_titles = ['Cross-Site Scripting (XSS)', 'SQL Injection', 'Outdated Software', 
                   'Insecure Direct Object References', 'Missing Security Headers', 
                   'Cross-Site Request Forgery (CSRF)', 'Insecure Cookie Configuration']
    vuln_descriptions = [
        'Reflected XSS vulnerability in search functionality',
        'SQL Injection vulnerability in login form',
        'Running outdated version with known security vulnerabilities',
        'Insecure direct object reference allows unauthorized access to resources',
        'Missing important security headers like X-Content-Type-Options, X-Frame-Options',
        'CSRF vulnerability in user profile update form',
        'Cookies set without secure and httpOnly flags'
    ]
    vuln_solutions = [
        'Implement proper input validation and output encoding',
        'Use prepared statements or ORM for database queries',
        'Update to the latest version and apply security patches',
        'Implement proper access controls and authorization checks',
        'Configure web server to add necessary security headers',
        'Implement anti-CSRF tokens in all forms',
        'Set secure and httpOnly flags for sensitive cookies'
    ]
    severities = ['high', 'high', 'medium', 'medium', 'low', 'medium', 'low']
    
    for i in range(min(metrics['vulnerabilities'], len(vuln_titles))):
        results['vulnerabilities'].append({
            'title': vuln_titles[i],
            'description': vuln_descriptions[i],
            'solution': vuln_solutions[i],
            'severity': severities[i],
            'references': 'https://owasp.org/Top10/'
        })
    
    # Render the HTML report
    return render_template('html_report.html',
                          target=target,
                          timestamp=timestamp,
                          metrics=metrics,
                          results=results)
                          
# @app.route('/download_html_report/<task_id>')
def download_html_report_helper(task_id):
    """Download the HTML report as a file."""
    task = scan_tasks.get(task_id)
    if not task:
        return "Tarea no encontrada", 404
    
    # Generate the HTML content
    html_content = generate_html_report(task_id)
    if isinstance(html_content, tuple):  # Error response
        return html_content
    
    # Create filename based on target and timestamp
    target = task['target']
    safe_target = ''.join(c if c.isalnum() else '_' for c in target)
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"OSINTdesk_Report_{safe_target}_{timestamp}.html"
    
    # Create response for file download
    response = make_response(html_content)
    response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'
    response.headers['Content-Type'] = 'text/html; charset=utf-8'
    
    return response

# Helper function to replace sanitize_filename
def safe_filename(filename):
    """Create a safe filename by removing special characters."""
    # Replace any non-alphanumeric characters (except for dots, hyphens, and underscores) with underscores
    return re.sub(r'[^\w\.\-]', '_', str(filename))

@app.route('/generate_html_report/<task_id>')
def generate_html_report_legacy(task_id):
    """Legacy version of the HTML report generator, redirects to the new route."""
    return redirect(url_for('generate_html_report', task_id=task_id))

@app.route('/gen_html_report/<task_id>')
def redirect_to_html_report(task_id):
    """Redirects to the HTML report generator."""
    return redirect(url_for('generate_html_report', task_id=task_id))

if __name__ == '__main__':
    # Make sure temp directory exists
    os.makedirs("temp", exist_ok=True)
    app.run(debug=True, host='0.0.0.0', port=5002) # debug=True for development, False for production 

# Add a redirect route to handle HTML report exports
@app.route('/export_html_report/<task_id>')
def export_html_report_redirect(task_id):
    """Redirects to the download HTML report function"""
    return redirect(url_for('download_html_report', task_id=task_id))