#!/bin/bash
# Script para instalar las herramientas OSINT faltantes

echo "===================================================="
echo "Instalando herramientas OSINT faltantes"
echo "===================================================="

# Activar el entorno virtual si existe
if [ -d "venv" ]; then
    source venv/bin/activate
fi

# Instalar herramientas DNS básicas
echo "[+] Asegurando que las herramientas DNS estén instaladas..."
if ! command -v host &> /dev/null; then
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        brew install bind
    else
        # Linux
        sudo apt-get update
        sudo apt-get install -y dnsutils
    fi
fi

# Instalar Sublist3r
echo "[+] Instalando Sublist3r..."
pip install sublist3r

# Comprobar la instalación
echo "[+] Verificando instalación de herramientas..."
if command -v host &> /dev/null; then
    echo "✅ La herramienta 'host' está instalada."
else
    echo "❌ Error: La herramienta 'host' no se pudo instalar."
fi

if python3 -c "import sublist3r" 2>/dev/null; then
    echo "✅ Sublist3r está instalado como módulo Python."
elif command -v sublist3r &> /dev/null; then
    echo "✅ El comando 'sublist3r' está disponible en el PATH."
else
    echo "⚠️ Advertencia: Sublist3r podría no estar correctamente instalado."
    echo "   Instalando Sublist3r desde GitHub..."
    
    # Clonar e instalar desde GitHub
    git clone https://github.com/aboul3la/Sublist3r.git /tmp/Sublist3r
    cd /tmp/Sublist3r
    pip install -r requirements.txt
    python setup.py install
    cd -
    
    # Crear enlace simbólico
    if [[ "$OSTYPE" == "darwin"* ]]; then
        ln -sf $(which python3) /usr/local/bin/sublist3r
        cat > /usr/local/bin/sublist3r << 'EOL'
#!/bin/bash
python3 -m sublist3r "$@"
EOL
        chmod +x /usr/local/bin/sublist3r
    else
        sudo ln -sf $(which python3) /usr/local/bin/sublist3r
        sudo bash -c 'cat > /usr/local/bin/sublist3r << EOL
#!/bin/bash
python3 -m sublist3r "\$@"
EOL'
        sudo chmod +x /usr/local/bin/sublist3r
    fi
fi

echo "===================================================="
echo "Instalación completada!"
echo ""
echo "Para reiniciar el portal, asegúrate de detener la instancia actual"
echo "y ejecutar: python app.py"
echo "===================================================="
