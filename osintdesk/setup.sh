#!/bin/bash
# Script de instalación para el Portal OSINT & Vulnerabilities

echo "===================================================="
echo "Instalando CyberScan Toolkit - Portal OSINT y Vulnerabilidades"
echo "===================================================="

# Crear entorno virtual
echo "[+] Creando entorno virtual..."
python3 -m venv venv
source venv/bin/activate

# Instalar dependencias de Python
echo "[+] Instalando dependencias de Python..."
pip install --upgrade pip
pip install -r requirements.txt

# Crear directorios necesarios
echo "[+] Creando directorios necesarios..."
mkdir -p temp uploads

# Herramientas OSINT básicas
echo "[+] Instalando herramientas OSINT básicas..."

# Comprueba si homebrew está instalado
if ! command -v brew &> /dev/null; then
    echo "[!] Homebrew no encontrado. Por favor, instala Homebrew primero."
    echo "    /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
    exit 1
fi

# Instalar herramientas básicas mediante brew
echo "[+] Instalando herramientas vía Homebrew..."
brew update
brew install whois nmap nikto sslscan exiftool

# Instalar testssl.sh desde GitHub si no existe
if [ ! -d "$HOME/testssl.sh" ]; then
    echo "[+] Instalando testssl.sh desde GitHub..."
    git clone --depth 1 https://github.com/drwetter/testssl.sh.git $HOME/testssl.sh
    chmod +x $HOME/testssl.sh/testssl.sh
    echo 'export PATH="$PATH:$HOME/testssl.sh"' >> ~/.zshrc
fi

# Instalar wafw00f con pip en lugar de brew
echo "[+] Instalando wafw00f con pip..."
pip install wafw00f

# Herramientas OSINT de Python
echo "[+] Instalando herramientas OSINT basadas en Python..."
pip install theHarvester dnsrecon socialscan h8mail sherlock-python metagoofil

# Instalar herramientas más específicas
echo "[+] Instalando subfinder y assetfinder..."
GO111MODULE=on go get -v github.com/projectdiscovery/subfinder/v2/cmd/subfinder
GO111MODULE=on go get -v github.com/tomnomnom/assetfinder

echo "[+] Instalando waybackurls y gau..."
GO111MODULE=on go get github.com/tomnomnom/waybackurls
GO111MODULE=on go get -u github.com/lc/gau

# Configurar API keys (placeholder)
echo "[+] Creando archivo de configuración para API keys..."
cat > api_keys.env << EOL
# Archivo de configuración para API keys
# Reemplaza los valores con tus propias API keys

SHODAN_API_KEY=tu_api_key_shodan
VIRUSTOTAL_API_KEY=tu_api_key_virustotal
CENSYS_API_ID=tu_api_id_censys
CENSYS_API_SECRET=tu_api_secret_censys
GITHUB_TOKEN=tu_token_github
EOL

echo "[+] Configurando archivo para h8mail..."
cat > ./temp/h8mail_config.ini << EOL
[h8mail]
; API keys para h8mail
hibp_key = tu_api_key_hibp
EOL

# Descargar herramientas Git populares
echo "[+] Clonando repositorios de herramientas útiles (si no existen)..."

# CMSeeK
if [ ! -d "$HOME/CMSeeK" ]; then
    git clone https://github.com/Tuhinshubhra/CMSeeK.git $HOME/CMSeeK
    cd $HOME/CMSeeK
    pip install -r requirements.txt
    cd -
fi

# GitGraber
if [ ! -d "/usr/local/bin/gitGraber" ]; then
    sudo mkdir -p /usr/local/bin/gitGraber
    sudo git clone https://github.com/hisxo/gitGraber.git /usr/local/bin/gitGraber
    cd /usr/local/bin/gitGraber
    pip install -r requirements.txt
    cd -
fi

# Mensaje final
echo "===================================================="
echo "Instalación completada!"
echo ""
echo "Para iniciar el portal, ejecuta:"
echo "source venv/bin/activate"
echo "python app.py"
echo ""
echo "No olvides configurar tus API keys en el archivo api_keys.env"
echo "===================================================="
