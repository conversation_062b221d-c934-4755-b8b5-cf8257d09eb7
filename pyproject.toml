[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "osint-karedesk"
version = "2.0.0"
description = "Comprehensive OSINT Security Analysis Framework"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "Security Analysis Framework Team"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Information Technology",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Security",
    "Topic :: Internet :: WWW/HTTP",
]
requires-python = ">=3.8"
dependencies = [
    "requests>=2.31.0",
    "dnspython>=2.4.0",
    "python-whois>=0.8.0",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0",
    "cryptography>=41.0.0",
    "urllib3>=2.0.0",
    "colorama>=0.4.6",
    "jinja2>=3.1.0",
    "python-dateutil>=2.8.2",
    "flask>=2.3.3",
    "flask-socketio>=5.3.6",
    "python-socketio>=5.9.0",
    "plotly>=5.17.0",
    "validators>=0.22.0",
    "psutil>=5.9.0",
    "tqdm>=4.66.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.0.0",
]
apis = [
    "shodan>=1.31.0",
    "censys>=2.2.0",
]
advanced = [
    "python-nmap>=0.7.1",
]

[project.scripts]
osint-analyze = "osint_framework:main"
osint-advanced = "advanced_osint_framework:main"
osint-portal = "web_portal:main"

[project.urls]
Homepage = "https://github.com/stoja88/osintkaredesk"
Repository = "https://github.com/stoja88/osintkaredesk.git"
Issues = "https://github.com/stoja88/osintkaredesk/issues"

# Black code formatting
[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# Flake8 linting
[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    ".eggs",
    "*.egg",
]

# MyPy type checking
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "whois.*",
    "dns.*",
    "shodan.*",
    "censys.*",
]
ignore_missing_imports = true

# Coverage configuration
[tool.coverage.run]
source = ["."]
omit = [
    "tests/*",
    "venv/*",
    ".venv/*",
    "*/site-packages/*",
    "setup.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.coverage.html]
directory = "htmlcov"
