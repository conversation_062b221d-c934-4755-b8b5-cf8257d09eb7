[tool:pytest]
# Pytest configuration for OSINT Framework
minversion = 6.0
addopts = 
    -ra
    --strict-markers
    --strict-config
    --cov=.
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=80
    -v

testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Markers for test categorization
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    network: Tests requiring network access
    
# Ignore warnings from dependencies
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*urllib3.*:urllib3.exceptions.InsecureRequestWarning
