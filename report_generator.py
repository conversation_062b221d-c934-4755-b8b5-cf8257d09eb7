#!/usr/bin/env python3
"""
Generador de Informes HTML para OSINT Framework
===============================================

Este módulo genera informes HTML profesionales a partir de los datos JSON
del análisis OSINT.
"""

import json
import datetime
from jinja2 import Template

class ReportGenerator:
    def __init__(self, json_file):
        with open(json_file, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
    
    def generate_html_report(self):
        """Generar informe HTML profesional"""
        
        html_template = """
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Informe OSINT - {{ data.executive_summary.domain }}</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
        }
        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.2em;
            margin-top: 10px;
        }
        .executive-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .executive-summary h2 {
            margin-top: 0;
            font-size: 1.8em;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .summary-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .summary-item .value {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }
        .summary-item .label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2c3e50;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            font-size: 1.6em;
        }
        .section h3 {
            color: #34495e;
            margin-top: 25px;
            font-size: 1.3em;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .info-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
        }
        .info-card h4 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 10px;
        }
        .dns-record {
            background: #e8f4f8;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #17a2b8;
        }
        .subdomain {
            background: #f0f8f0;
            padding: 8px 12px;
            margin: 3px;
            border-radius: 15px;
            display: inline-block;
            font-size: 0.9em;
            border: 1px solid #28a745;
        }
        .recommendation {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .recommendation.high {
            border-left: 5px solid #dc3545;
            background: #f8d7da;
        }
        .recommendation.medium {
            border-left: 5px solid #fd7e14;
            background: #fff3cd;
        }
        .recommendation.low {
            border-left: 5px solid #28a745;
            background: #d4edda;
        }
        .risk-level {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .risk-high { background: #dc3545; color: white; }
        .risk-medium { background: #fd7e14; color: white; }
        .risk-low { background: #28a745; color: white; }
        .security-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            margin: 5px 0;
            border-radius: 5px;
        }
        .security-header.present {
            background: #d4edda;
            border-left: 4px solid #28a745;
        }
        .security-header.absent {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .status-present {
            background: #28a745;
            color: white;
        }
        .status-absent {
            background: #dc3545;
            color: white;
        }
        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 2px solid #e9ecef;
            text-align: center;
            color: #6c757d;
            font-size: 0.9em;
        }
        .ethical-notice {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-style: italic;
        }
        pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🔍 Informe de Análisis OSINT</h1>
            <div class="subtitle">{{ data.executive_summary.domain }}</div>
            <div class="subtitle">{{ data.executive_summary.analysis_date }}</div>
        </div>

        <!-- Executive Summary -->
        <div class="executive-summary">
            <h2>📊 Resumen Ejecutivo</h2>
            <div class="summary-grid">
                <div class="summary-item">
                    <span class="value">{{ data.executive_summary.domain }}</span>
                    <span class="label">Dominio Analizado</span>
                </div>
                <div class="summary-item">
                    <span class="value risk-level risk-{{ data.executive_summary.risk_level.lower() }}">
                        {{ data.executive_summary.risk_level }}
                    </span>
                    <span class="label">Nivel de Riesgo</span>
                </div>
                <div class="summary-item">
                    <span class="value">{{ data.executive_summary.total_findings }}</span>
                    <span class="label">Recomendaciones</span>
                </div>
                <div class="summary-item">
                    <span class="value">{{ (data.detailed_findings.osint.all_subdomains|length if data.detailed_findings.osint.all_subdomains else 0) or (data.detailed_findings.osint.subdomains|length if data.detailed_findings.osint.subdomains else 0) }}</span>
                    <span class="label">Subdominios</span>
                </div>
            </div>
        </div>

        <!-- Reconnaissance Section -->
        <div class="section">
            <h2>🔍 Reconocimiento</h2>
            
            <div class="info-grid">
                <div class="info-card">
                    <h4>📡 Información DNS</h4>
                    {% if data.detailed_findings.reconnaissance.ip_address %}
                    <p><strong>IP Address:</strong> {{ data.detailed_findings.reconnaissance.ip_address }}</p>
                    {% endif %}
                    
                    {% if data.detailed_findings.reconnaissance.dns_records %}
                    {% for record_type, records in data.detailed_findings.reconnaissance.dns_records.items() %}
                    <div class="dns-record">
                        <strong>{{ record_type }}:</strong>
                        {% for record in records %}
                        <div>{{ record }}</div>
                        {% endfor %}
                    </div>
                    {% endfor %}
                    {% endif %}
                </div>

                {% if data.detailed_findings.reconnaissance.ssl_info %}
                <div class="info-card">
                    <h4>🔒 Información SSL/TLS</h4>
                    <p><strong>Versión:</strong> {{ data.detailed_findings.reconnaissance.ssl_info.version }}</p>
                    {% if data.detailed_findings.reconnaissance.ssl_info.certificate %}
                    <p><strong>Emisor:</strong> {{ data.detailed_findings.reconnaissance.ssl_info.certificate.issuer.organizationName if data.detailed_findings.reconnaissance.ssl_info.certificate.issuer.organizationName else 'N/A' }}</p>
                    <p><strong>Válido hasta:</strong> {{ data.detailed_findings.reconnaissance.ssl_info.certificate.notAfter }}</p>
                    {% endif %}
                </div>
                {% endif %}
            </div>

            {% if data.detailed_findings.reconnaissance.web_info %}
            <h3>🤖 Archivos Web</h3>
            {% if data.detailed_findings.reconnaissance.web_info.robots_txt %}
            <h4>robots.txt</h4>
            <pre>{{ data.detailed_findings.reconnaissance.web_info.robots_txt[:500] }}{% if data.detailed_findings.reconnaissance.web_info.robots_txt|length > 500 %}...{% endif %}</pre>
            {% endif %}
            {% endif %}
        </div>

        <!-- OSINT Section -->
        <div class="section">
            <h2>🌐 Inteligencia de Fuentes Abiertas (OSINT)</h2>
            
            {% if data.detailed_findings.osint.all_subdomains or data.detailed_findings.osint.subdomains %}
            <h3>Subdominios Encontrados</h3>
            <div>
                {% for subdomain in (data.detailed_findings.osint.all_subdomains or data.detailed_findings.osint.subdomains or []) %}
                <span class="subdomain">{{ subdomain }}</span>
                {% endfor %}
            </div>
            {% endif %}

            {% if data.detailed_findings.osint.technologies %}
            <h3>💻 Tecnologías Detectadas</h3>
            <div class="info-card">
                {% if data.detailed_findings.osint.technologies.web_server %}
                <p><strong>Servidor Web:</strong> {{ data.detailed_findings.osint.technologies.web_server }}</p>
                {% endif %}
                {% if data.detailed_findings.osint.technologies.detected %}
                <p><strong>Tecnologías:</strong> {{ data.detailed_findings.osint.technologies.detected|join(', ') }}</p>
                {% endif %}
            </div>
            {% endif %}
        </div>

        <!-- Security Analysis -->
        <div class="section">
            <h2>🛡️ Análisis de Seguridad</h2>
            
            {% if data.detailed_findings.vulnerabilities.security_headers %}
            <h3>Cabeceras de Seguridad</h3>
            {% for header, status in data.detailed_findings.vulnerabilities.security_headers.items() %}
            <div class="security-header {% if status != 'Ausente' %}present{% else %}absent{% endif %}">
                <span>{{ header }}</span>
                <span class="status-badge {% if status != 'Ausente' %}status-present{% else %}status-absent{% endif %}">
                    {% if status != 'Ausente' %}Presente{% else %}Ausente{% endif %}
                </span>
            </div>
            {% endfor %}
            {% endif %}
        </div>

        <!-- Recommendations -->
        <div class="section">
            <h2>📋 Recomendaciones</h2>
            {% for rec in data.recommendations %}
            <div class="recommendation {{ rec.priority.lower() }}">
                <h4>{{ rec.category }} - {{ rec.issue }}</h4>
                <p>{{ rec.recommendation }}</p>
                <small><strong>Prioridad:</strong> {{ rec.priority }}</small>
            </div>
            {% endfor %}
        </div>

        <!-- Ethical Notice -->
        <div class="ethical-notice">
            <strong>⚖️ Aviso Ético:</strong> {{ data.detailed_findings.ethical_notice }}
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>Informe generado el {{ datetime.datetime.now().strftime('%d/%m/%Y %H:%M:%S') }}</p>
            <p>OSINT Security Analysis Framework v1.0</p>
        </div>
    </div>
</body>
</html>
        """
        
        template = Template(html_template)
        html_content = template.render(data=self.data, datetime=datetime)
        
        # Guardar archivo HTML
        domain = self.data['executive_summary']['domain']
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"osint_report_{domain}_{timestamp}.html"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ Informe HTML generado: {filename}")
        return filename

def main():
    import sys
    if len(sys.argv) != 2:
        print("Uso: python report_generator.py <archivo_json>")
        sys.exit(1)
    
    json_file = sys.argv[1]
    generator = ReportGenerator(json_file)
    generator.generate_html_report()

if __name__ == "__main__":
    main() 