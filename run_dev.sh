#!/bin/bash
# Script para ejecutar OSINTdesk en modo desarrollo

echo "===================================================="
echo "               OSINTdesk - Modo Desarrollo          "
echo "===================================================="

# Verificar entorno virtual
if [ ! -d "venv" ]; then
    echo "[+] Creando entorno virtual..."
    python -m venv venv
fi

# Activar entorno virtual
source venv/bin/activate

# Verificar dependencias
if [ ! -f "venv/installed" ]; then
    echo "[+] Instalando dependencias..."
    pip install -r requirements.txt
    touch venv/installed
fi

# Crear directorios necesarios
mkdir -p reports temp

# Exportar variables de entorno
export FLASK_APP=osintdesk/app.py
export FLASK_DEBUG=1

# Verificar si ya está en ejecución
PORT=5001
if lsof -i :$PORT > /dev/null; then
    echo "[!] El puerto $PORT ya está en uso. Usando puerto alternativo 5002."
    PORT=5002
fi

echo "[+] Iniciando OSINTdesk en http://localhost:$PORT"
echo "===================================================="
python -c "from osintdesk.app import app; app.run(port=$PORT, debug=True)" 