#!/bin/bash
# Script para construir y ejecutar OSINTdesk en Docker

echo "===================================================="
echo "               OSINTdesk - Modo Docker              "
echo "===================================================="

# Verificar Docker
if ! command -v docker &> /dev/null; then
    echo "[ERROR] Docker no está instalado. Por favor, instala Docker."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "[ERROR] Docker Compose no está instalado. Por favor, instala Docker Compose."
    exit 1
fi

# Verificar archivo .env
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        echo "[+] Creando .env a partir de .env.example..."
        cp .env.example .env
    else
        echo "[+] Creando archivo .env básico..."
        cat > .env << EOL
# OSINTdesk API Keys
SHODAN_API_KEY=
CENSYS_API_ID=
CENSYS_API_SECRET=
VIRUSTOTAL_API_KEY=
HUNTER_API_KEY=
SECURITY_TRAILS_API_KEY=
MAXMIND_LICENSE_KEY=
EOL
    fi
    echo "[!] Por favor, edita el archivo .env para agregar tus claves API."
fi

# Crear directorios necesarios
mkdir -p reports temp

# Construir y ejecutar con Docker Compose
echo "[+] Construyendo y ejecutando OSINTdesk en Docker..."
echo "===================================================="
docker-compose up --build -d

# Verificar estado
if [ $? -eq 0 ]; then
    echo "[+] OSINTdesk está en ejecución en http://localhost:5001"
    echo "[+] Para ver los logs: docker-compose logs -f"
    echo "[+] Para detener: docker-compose down"
else
    echo "[ERROR] Hubo un problema al iniciar OSINTdesk."
    echo "        Verifica los logs con: docker-compose logs"
fi

echo "====================================================" 