<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="greenGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00ff00;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00aa00;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Fondo -->
  <rect width="32" height="32" fill="#000000" rx="4" ry="4"/>
  
  <!-- Letra F estilizada -->
  <g fill="url(#greenGrad)" stroke="#00ff00" stroke-width="0.5">
    <rect x="8" y="8" width="2" height="16"/>
    <rect x="8" y="8" width="12" height="2"/>
    <rect x="8" y="15" width="8" height="2"/>
  </g>
  
  <!-- Letra H estilizada -->
  <g fill="url(#greenGrad)" stroke="#00ff00" stroke-width="0.5">
    <rect x="22" y="8" width="2" height="16"/>
    <rect x="26" y="8" width="2" height="16"/>
    <rect x="22" y="15" width="6" height="2"/>
  </g>
  
  <!-- Puntos decorativos -->
  <circle cx="6" cy="6" r="1" fill="#00ff00" opacity="0.6"/>
  <circle cx="26" cy="6" r="1" fill="#00ff00" opacity="0.6"/>
  <circle cx="6" cy="26" r="1" fill="#00ff00" opacity="0.6"/>
  <circle cx="26" cy="26" r="1" fill="#00ff00" opacity="0.6"/>
  
  <!-- Borde -->
  <rect width="30" height="30" x="1" y="1" fill="none" stroke="#00ff00" stroke-width="0.5" rx="3" ry="3" opacity="0.7"/>
</svg>
