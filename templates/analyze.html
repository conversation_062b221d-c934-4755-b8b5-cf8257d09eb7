{% extends "base.html" %}

{% block title %}Análisis OSINT - OSINT Portal{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="text-white mb-0 fade-in-up">
            <i class="fas fa-search me-3"></i>
            Análisis OSINT
        </h1>
        <p class="text-white-50 mt-2 fade-in-up">Herramienta de análisis de seguridad y reconocimiento</p>
    </div>
</div>

<!-- Formulario de análisis -->
<div class="row">
    <div class="col-lg-8 fade-in-up">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-globe me-2"></i>
                    Configurar Análisis
                </h5>
            </div>
            <div class="card-body">
                <form id="analysisForm">
                    <!-- Dominio objetivo -->
                    <div class="mb-4">
                        <label for="domain" class="form-label">
                            <i class="fas fa-link me-1"></i>
                            Dominio Objetivo *
                        </label>
                        <input type="text" class="form-control form-control-lg" id="domain" 
                               placeholder="ejemplo.com" required>
                        <div class="form-text">Ingrese el dominio sin protocolo (http/https) ni www</div>
                    </div>

                    <!-- Tipo de análisis -->
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="fas fa-layer-group me-1"></i>
                            Tipo de Análisis
                        </label>
                        <div class="btn-group w-100" role="group">
                            <input type="radio" class="btn-check" name="analysisType" id="basicAnalysis" value="basic" checked>
                            <label class="btn btn-outline-primary" for="basicAnalysis">
                                <i class="fas fa-search me-1"></i>
                                Análisis Básico
                            </label>
                            
                            <input type="radio" class="btn-check" name="analysisType" id="advancedAnalysis" value="advanced">
                            <label class="btn btn-outline-danger" for="advancedAnalysis">
                                <i class="fas fa-rocket me-1"></i>
                                Análisis Avanzado
                            </label>
                        </div>
                        <div class="form-text">
                            <span id="analysisTypeDescription">
                                <strong>Básico:</strong> DNS, SSL, subdominios, tecnologías, cabeceras de seguridad
                            </span>
                        </div>
                    </div>

                    <!-- Opciones de análisis básico -->
                    <div class="mb-4" id="basicOptions">
                        <label class="form-label">
                            <i class="fas fa-cogs me-1"></i>
                            Módulos de Análisis Básico
                        </label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="dnsAnalysis" checked>
                                    <label class="form-check-label" for="dnsAnalysis">
                                        Reconocimiento DNS
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="sslAnalysis" checked>
                                    <label class="form-check-label" for="sslAnalysis">
                                        Análisis SSL/TLS
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="subdomainEnum" checked>
                                    <label class="form-check-label" for="subdomainEnum">
                                        Enumeración de Subdominios
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="techDetection" checked>
                                    <label class="form-check-label" for="techDetection">
                                        Detección de Tecnologías
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="securityHeaders" checked>
                                    <label class="form-check-label" for="securityHeaders">
                                        Cabeceras de Seguridad
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="webFiles" checked>
                                    <label class="form-check-label" for="webFiles">
                                        Archivos Web (robots.txt, sitemap.xml)
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Opciones de análisis avanzado -->
                    <div class="mb-4" id="advancedOptions" style="display: none;">
                        <label class="form-label">
                            <i class="fas fa-rocket me-1"></i>
                            Herramientas Avanzadas
                        </label>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-1"></i>
                            El análisis avanzado incluye: Nuclei (vulnerabilidades), Nmap (puertos), Subfinder/Amass (subdominios avanzados), 
                            Wayback Machine (histórico), WhatWeb (fingerprinting), Gobuster/FFUF (fuzzing)
                        </div>
                        
                        <!-- API Keys opcionales -->
                        <div class="row">
                            <div class="col-md-6">
                                <label for="shodanKey" class="form-label">
                                    <i class="fas fa-key me-1"></i>
                                    Shodan API Key (opcional)
                                </label>
                                <input type="text" class="form-control" id="shodanKey" placeholder="Tu API key de Shodan">
                            </div>
                            <div class="col-md-6">
                                <label for="censysId" class="form-label">
                                    <i class="fas fa-key me-1"></i>
                                    Censys API ID (opcional)
                                </label>
                                <input type="text" class="form-control" id="censysId" placeholder="Tu API ID de Censys">
                            </div>
                        </div>
                        
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            <strong>Advertencia:</strong> El análisis avanzado puede tardar varios minutos y realizar múltiples peticiones.
                            Asegúrese de tener autorización completa.
                        </div>
                    </div>

                    <!-- Confirmación ética -->
                    <div class="mb-4">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Consideraciones Éticas</h6>
                            <ul class="mb-2">
                                <li>Solo analice dominios para los que tenga autorización explícita</li>
                                <li>Este framework utiliza únicamente fuentes públicas</li>
                                <li>Respete los términos de servicio y límites de velocidad</li>
                                <li>Use esta herramienta solo con fines educativos o legítimos</li>
                            </ul>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="ethicalConfirmation" required>
                                <label class="form-check-label" for="ethicalConfirmation">
                                    <strong>Confirmo que tengo autorización para analizar el dominio especificado</strong>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Botón de análisis -->
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg px-5" id="analyzeBtn">
                            <i class="fas fa-play me-2"></i>
                            Iniciar Análisis
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Panel de información -->
    <div class="col-lg-4 fade-in-up">
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Información del Análisis
                </h6>
            </div>
            <div class="card-body">
                <div class="analysis-info">
                    <div class="info-item mb-3">
                        <i class="fas fa-clock text-primary me-2"></i>
                        <strong>Duración:</strong> 30-60 segundos
                    </div>
                    <div class="info-item mb-3">
                        <i class="fas fa-shield-alt text-success me-2"></i>
                        <strong>Métodos:</strong> Solo fuentes públicas
                    </div>
                    <div class="info-item mb-3">
                        <i class="fas fa-file-alt text-info me-2"></i>
                        <strong>Informe:</strong> JSON y HTML
                    </div>
                    <div class="info-item">
                        <i class="fas fa-chart-bar text-warning me-2"></i>
                        <strong>Métricas:</strong> Nivel de riesgo automático
                    </div>
                </div>
            </div>
        </div>

        <!-- Ejemplos de dominios -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    Ejemplos de Uso
                </h6>
            </div>
            <div class="card-body">
                <p class="small text-muted mb-3">Ejemplos de dominios para pruebas:</p>
                <div class="example-domains">
                    <button class="btn btn-outline-secondary btn-sm mb-2 w-100" onclick="setDomain('example.com')">
                        example.com
                    </button>
                    <button class="btn btn-outline-secondary btn-sm mb-2 w-100" onclick="setDomain('httpbin.org')">
                        httpbin.org
                    </button>
                    <button class="btn btn-outline-secondary btn-sm mb-2 w-100" onclick="setDomain('testphp.vulnweb.com')">
                        testphp.vulnweb.com
                    </button>
                </div>
                <small class="text-muted">
                    <i class="fas fa-exclamation-circle me-1"></i>
                    Solo use dominios para los que tenga autorización
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Panel de progreso del análisis (oculto inicialmente) -->
<div class="row mt-4" id="progressPanel" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-spinner fa-spin me-2"></i>
                    Análisis en Progreso
                </h6>
            </div>
            <div class="card-body">
                <div class="progress-info mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Analizando: <strong id="currentDomain"></strong></span>
                        <span id="progressPercentage">0%</span>
                    </div>
                    <div class="progress mt-2">
                        <div id="progressBarMain" class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 0%"></div>
                    </div>
                    <div class="mt-2">
                        <small id="currentPhase" class="text-muted d-block">Iniciando análisis...</small>
                        <div class="d-flex align-items-center mt-1">
                            <small class="text-info me-2">
                                <i class="fas fa-tools me-1"></i>
                                <span id="currentTool">python</span>
                            </small>
                            <small class="text-muted">
                                Paso <span id="currentStep">1</span> de <span id="totalSteps">10</span>
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Fases del análisis -->
                <div class="analysis-phases">
                    <div class="row text-center">
                        <div class="col-2">
                            <div class="phase-indicator" id="phase1">
                                <i class="fas fa-server"></i>
                                <small>DNS</small>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="phase-indicator" id="phase2">
                                <i class="fas fa-lock"></i>
                                <small>SSL</small>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="phase-indicator" id="phase3">
                                <i class="fas fa-sitemap"></i>
                                <small>Subdominios</small>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="phase-indicator" id="phase4">
                                <i class="fas fa-code"></i>
                                <small>Tecnologías</small>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="phase-indicator" id="phase5">
                                <i class="fas fa-shield-alt"></i>
                                <small>Seguridad</small>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="phase-indicator" id="phase6">
                                <i class="fas fa-file-alt"></i>
                                <small>Informe</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.analysis-info .info-item {
    padding: 8px 0;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.analysis-info .info-item:last-child {
    border-bottom: none;
}

.example-domains .btn {
    font-size: 0.85rem;
}

.phase-indicator {
    padding: 15px;
    border-radius: 10px;
    background: rgba(255,255,255,0.8);
    margin: 5px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.phase-indicator i {
    display: block;
    font-size: 1.5rem;
    margin-bottom: 5px;
    color: #6c757d;
}

.phase-indicator small {
    font-size: 0.75rem;
    color: #6c757d;
}

.phase-indicator.active {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border-color: #3498db;
    transform: scale(1.1);
}

.phase-indicator.active i,
.phase-indicator.active small {
    color: white;
}

.phase-indicator.completed {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    border-color: #27ae60;
}

.phase-indicator.completed i,
.phase-indicator.completed small {
    color: white;
}

.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('analysisForm');
    const progressPanel = document.getElementById('progressPanel');
    
    form.addEventListener('submit', handleFormSubmit);
    
    // Manejar cambio de tipo de análisis
    document.querySelectorAll('input[name="analysisType"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'advanced') {
                document.getElementById('basicOptions').style.display = 'none';
                document.getElementById('advancedOptions').style.display = 'block';
                document.getElementById('analysisTypeDescription').innerHTML = 
                    '<strong>Avanzado:</strong> Incluye todas las herramientas profesionales: Nuclei, Nmap, Amass, y más';
            } else {
                document.getElementById('basicOptions').style.display = 'block';
                document.getElementById('advancedOptions').style.display = 'none';
                document.getElementById('analysisTypeDescription').innerHTML = 
                    '<strong>Básico:</strong> DNS, SSL, subdominios, tecnologías, cabeceras de seguridad';
            }
        });
    });
});

function setDomain(domain) {
    document.getElementById('domain').value = domain;
}

function handleFormSubmit(e) {
    e.preventDefault();
    
    const domain = document.getElementById('domain').value.trim();
    const ethicalConfirmation = document.getElementById('ethicalConfirmation').checked;
    
    if (!domain) {
        alert('Por favor ingrese un dominio válido');
        return;
    }
    
    if (!ethicalConfirmation) {
        alert('Debe confirmar que tiene autorización para analizar este dominio');
        return;
    }
    
    startAnalysis(domain);
}

function startAnalysis(domain) {
    // Mostrar panel de progreso
    document.getElementById('progressPanel').style.display = 'block';
    document.getElementById('currentDomain').textContent = domain;
    
    // Deshabilitar formulario
    document.getElementById('analyzeBtn').disabled = true;
    document.getElementById('analyzeBtn').innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Analizando...';
    
    // Resetear indicadores de fase
    resetPhaseIndicators();
    
    // Obtener tipo de análisis y API keys
    const analysisType = document.querySelector('input[name="analysisType"]:checked').value;
    const isAdvanced = analysisType === 'advanced';
    
    const requestData = {
        domain: domain,
        authorized: true,
        advanced: isAdvanced
    };
    
    // Si es análisis avanzado, incluir API keys si están disponibles
    if (isAdvanced) {
        const shodanKey = document.getElementById('shodanKey').value.trim();
        const censysId = document.getElementById('censysId').value.trim();
        
        requestData.api_keys = {};
        if (shodanKey) {
            requestData.api_keys.shodan = shodanKey;
        }
        if (censysId) {
            requestData.api_keys.censys = { id: censysId };
        }
    }
    
    // Realizar petición de análisis
    fetch('/api/start_analysis', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            throw new Error(data.error || 'Error desconocido');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error al iniciar el análisis: ' + error.message);
        resetForm();
    });
}

function resetPhaseIndicators() {
    for (let i = 1; i <= 6; i++) {
        const phase = document.getElementById(`phase${i}`);
        phase.classList.remove('active', 'completed');
    }
}

function updatePhaseIndicator(phaseText) {
    // Mapear texto de fase a número
    const phaseMap = {
        'Reconocimiento DNS': 1,
        'Análisis SSL': 2,
        'Subdominios': 3,
        'Tecnologías': 4,
        'Seguridad': 5,
        'Informe': 6,
        'Completado': 6
    };
    
    const phaseNumber = phaseMap[phaseText] || 1;
    
    // Marcar fases anteriores como completadas
    for (let i = 1; i < phaseNumber; i++) {
        const phase = document.getElementById(`phase${i}`);
        phase.classList.remove('active');
        phase.classList.add('completed');
    }
    
    // Marcar fase actual como activa
    if (phaseNumber <= 6) {
        const currentPhase = document.getElementById(`phase${phaseNumber}`);
        currentPhase.classList.add('active');
        currentPhase.classList.remove('completed');
    }
}

function resetForm() {
    document.getElementById('analyzeBtn').disabled = false;
    document.getElementById('analyzeBtn').innerHTML = '<i class="fas fa-play me-2"></i>Iniciar Análisis';
    document.getElementById('progressPanel').style.display = 'none';
}

// Override de funciones globales para esta página
function updateAnalysisProgress(data) {
    const progressBar = document.getElementById('progressBarMain');
    const progressPercentage = document.getElementById('progressPercentage');
    const currentPhase = document.getElementById('currentPhase');
    const currentTool = document.getElementById('currentTool');
    const currentStep = document.getElementById('currentStep');
    const totalSteps = document.getElementById('totalSteps');

    if (progressBar) {
        progressBar.style.width = data.progress + '%';
        progressPercentage.textContent = data.progress + '%';
        currentPhase.textContent = data.message;

        // Actualizar información de herramientas
        if (data.tool && currentTool) {
            currentTool.textContent = data.tool;
            // Cambiar color según la herramienta
            currentTool.className = getToolColor(data.tool);
        }

        if (data.step && currentStep) {
            currentStep.textContent = data.step;
        }

        if (data.total_steps && totalSteps) {
            totalSteps.textContent = data.total_steps;
        }

        updatePhaseIndicator(data.phase);
    }

    // También llamar a la función global si existe
    if (window.updateAnalysisProgress) {
        window.updateAnalysisProgress(data);
    }
}

function getToolColor(tool) {
    const toolColors = {
        'python': 'text-success',
        'dig': 'text-primary',
        'openssl': 'text-warning',
        'crt.sh': 'text-info',
        'subfinder': 'text-danger',
        'nmap': 'text-dark',
        'whatweb': 'text-secondary',
        'curl': 'text-muted'
    };
    return toolColors[tool] || 'text-info';
}

function completeAnalysis(data) {
    // Completar todas las fases
    for (let i = 1; i <= 6; i++) {
        const phase = document.getElementById(`phase${i}`);
        phase.classList.remove('active');
        phase.classList.add('completed');
    }
    
    setTimeout(() => {
        resetForm();
        window.location.href = '/reports';
    }, 3000);
    
    // También llamar a la función global si existe
    if (window.completeAnalysis) {
        window.completeAnalysis(data);
    }
}

function errorAnalysis(data) {
    alert('Error en el análisis: ' + data.error);
    resetForm();
    
    // También llamar a la función global si existe
    if (window.errorAnalysis) {
        window.errorAnalysis(data);
    }
}
</script>
{% endblock %} 