<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}OSINT Portal - Security Analysis Framework{% endblock %}</title>

    <!-- Metadatos -->
    <meta name="description" content="Portal OSINT profesional para análisis de seguridad y reconocimiento técnico avanzado">
    <meta name="keywords" content="OSINT, Security Analysis, Penetration Testing, Cybersecurity">
    <meta name="author" content="OSINT Portal">
    <meta name="robots" content="noindex, nofollow">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="OSINT Portal - Security Analysis Framework">
    <meta property="og:description" content="Portal OSINT profesional para análisis de seguridad y reconocimiento técnico avanzado">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:title" content="OSINT Portal - Security Analysis Framework">
    <meta property="twitter:description" content="Portal OSINT profesional para análisis de seguridad y reconocimiento técnico avanzado">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <!-- Plotly.js -->
    <script src="https://cdn.plot.ly/plotly-2.25.2.min.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --dark-color: #34495e;
            --light-color: #ecf0f1;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: rgba(44, 62, 80, 0.95) !important;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .navbar-brand {
            font-weight: bold;
            color: #fff !important;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.8) !important;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: #3498db !important;
        }

        .main-container {
            margin-top: 2rem;
            margin-bottom: 2rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
            border: none;
            border-radius: 25px;
            padding: 10px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color), #2ecc71);
            border: none;
            border-radius: 25px;
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color), #e67e22);
            border: none;
            border-radius: 25px;
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color), #c0392b);
            border: none;
            border-radius: 25px;
        }

        .stat-card {
            text-align: center;
            padding: 2rem;
            border-radius: 15px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: scale(1.05);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 1rem;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .progress {
            height: 20px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.3);
        }

        .progress-bar {
            border-radius: 10px;
            background: linear-gradient(90deg, var(--success-color), var(--secondary-color));
        }

        .risk-high {
            color: var(--danger-color);
        }

        .risk-medium {
            color: var(--warning-color);
        }

        .risk-low {
            color: var(--success-color);
        }

        .analysis-status {
            position: fixed;
            top: 80px;
            right: 20px;
            z-index: 1050;
            width: 300px;
        }

        .footer {
            background: rgba(44, 62, 80, 0.95);
            color: white;
            text-align: center;
            padding: 2rem;
            margin-top: 3rem;
            backdrop-filter: blur(10px);
        }

        /* Animaciones */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shield-alt me-2"></i>
                OSINT Portal
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/analyze">
                            <i class="fas fa-search me-1"></i>Analizar
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/reports">
                            <i class="fas fa-file-alt me-1"></i>Informes
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container main-container" style="margin-top: 80px;">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="row">
                    <div class="col-12">
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <!-- Analysis Status (Fixed Position) -->
    <div id="analysisStatus" class="analysis-status" style="display: none;">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-spinner fa-spin me-2"></i>
                    Análisis en Progreso
                </h6>
            </div>
            <div class="card-body">
                <div id="progressInfo">
                    <small id="progressDomain" class="text-muted"></small>
                    <div class="progress mt-2">
                        <div id="progressBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small id="progressMessage" class="text-muted mt-1 d-block"></small>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p class="mb-0">
                &copy; 2024 OSINT Security Analysis Framework -
                <span class="text-warning">Solo para uso ético y autorizado</span>
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Socket.IO Connection -->
    <script>
        const socket = io();
        
        socket.on('connect', function() {
            console.log('Conectado al servidor OSINT');
        });
        
        socket.on('analysis_progress', function(data) {
            updateAnalysisProgress(data);
        });
        
        socket.on('analysis_complete', function(data) {
            completeAnalysis(data);
        });
        
        socket.on('analysis_error', function(data) {
            errorAnalysis(data);
        });
        
        function updateAnalysisProgress(data) {
            const statusDiv = document.getElementById('analysisStatus');
            const progressBar = document.getElementById('progressBar');
            const progressMessage = document.getElementById('progressMessage');
            const progressDomain = document.getElementById('progressDomain');
            
            statusDiv.style.display = 'block';
            progressBar.style.width = data.progress + '%';
            progressMessage.textContent = data.message;
            progressDomain.textContent = `Analizando: ${data.domain}`;
        }
        
        function completeAnalysis(data) {
            const statusDiv = document.getElementById('analysisStatus');
            setTimeout(() => {
                statusDiv.style.display = 'none';
                location.reload(); // Recargar para mostrar nuevos datos
            }, 2000);
        }
        
        function errorAnalysis(data) {
            const statusDiv = document.getElementById('analysisStatus');
            statusDiv.innerHTML = `
                <div class="card">
                    <div class="card-header bg-danger">
                        <h6 class="mb-0 text-white">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Error en Análisis
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="text-danger">${data.error}</p>
                        <button class="btn btn-sm btn-secondary" onclick="this.parentElement.parentElement.parentElement.style.display='none'">
                            Cerrar
                        </button>
                    </div>
                </div>
            `;
        }
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html> 