{% extends "base.html" %}

{% block title %}Dashboard - Formula Hacking OSINT Portal{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header del Dashboard -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center mb-2">
                <div class="me-4">
                    <img src="/static/images/formula-hacking-logo.png" alt="Formula Hacking" height="60">
                </div>
                <div class="border-start border-secondary ps-4">
                    <h2><i class="fas fa-tachometer-alt me-2"></i>Dashboard OSINT</h2>
                </div>
            </div>
            <p class="text-muted">Panel de control para análisis de seguridad y reconocimiento profesional</p>
        </div>
    </div>

    <!-- Estadísticas rápidas -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-file-alt fa-2x mb-2"></i>
                    <h4>{{ stats.total_reports or 0 }}</h4>
                    <small>Total Análisis</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <h4>{{ stats.high_risk or 0 }}</h4>
                    <small>Riesgo Alto</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation fa-2x mb-2"></i>
                    <h4>{{ stats.medium_risk or 0 }}</h4>
                    <small>Riesgo Medio</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h4>{{ stats.low_risk or 0 }}</h4>
                    <small>Riesgo Bajo</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Acciones rápidas -->
    <div class="row mb-4">
        <div class="col-md-6 mb-3">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-search fa-3x text-primary mb-3"></i>
                    <h5>Nuevo Análisis</h5>
                    <p class="text-muted">Inicia un análisis OSINT completo de un dominio</p>
                    <a href="/analyze" class="btn btn-primary">
                        <i class="fas fa-play me-1"></i>
                        Comenzar Análisis
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-3">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-file-alt fa-3x text-success mb-3"></i>
                    <h5>Ver Reportes</h5>
                    <p class="text-muted">Gestiona y visualiza análisis anteriores</p>
                    <a href="/reports" class="btn btn-success">
                        <i class="fas fa-list me-1"></i>
                        Ver Reportes
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Último análisis -->
    {% if stats.latest_analysis %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>
                        Último Análisis
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Dominio:</strong> {{ stats.latest_analysis.domain }}</p>
                            <p><strong>Fecha:</strong> {{ stats.latest_analysis.date }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Nivel de Riesgo:</strong> 
                                <span class="badge bg-{% if stats.latest_analysis.risk_level == 'Alto' %}danger{% elif stats.latest_analysis.risk_level == 'Medio' %}warning{% else %}success{% endif %}">
                                    {{ stats.latest_analysis.risk_level }}
                                </span>
                            </p>
                            <p><strong>Hallazgos:</strong> {{ stats.latest_analysis.findings }}</p>
                        </div>
                    </div>
                    <div class="mt-3">
                        {% if stats.latest_analysis.has_html %}
                        <a href="/visualize/{{ stats.latest_analysis.filename }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>
                            Ver Análisis
                        </a>
                        {% endif %}
                        <a href="/reports" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-list me-1"></i>
                            Todos los Reportes
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Información del sistema -->
    <div class="row">
        <div class="col-md-6 mb-3">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Información del Framework
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li><i class="fas fa-check text-success me-2"></i>Análisis DNS completo</li>
                        <li><i class="fas fa-check text-success me-2"></i>Enumeración de subdominios</li>
                        <li><i class="fas fa-check text-success me-2"></i>Análisis SSL/TLS</li>
                        <li><i class="fas fa-check text-success me-2"></i>Cabeceras de seguridad</li>
                        <li><i class="fas fa-check text-success me-2"></i>Detección de tecnologías</li>
                        <li><i class="fas fa-check text-success me-2"></i>Informes ejecutivos</li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-3">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        Consideraciones Éticas
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Importante:</strong> Este framework debe utilizarse únicamente con autorización explícita del propietario del dominio y con fines educativos o de seguridad legítimos.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Actualizar estadísticas cada 30 segundos de forma simple
setInterval(function() {
    fetch('/api/stats')
        .then(response => response.json())
        .then(data => {
            // Actualizar solo si hay cambios
            console.log('Stats updated');
        })
        .catch(error => {
            console.log('Error updating stats:', error);
        });
}, 30000);
</script>
{% endblock %} 