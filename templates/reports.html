{% extends "base.html" %}

{% block title %}Informes - OSINT Portal{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-file-alt me-2"></i>Informes de Análisis</h2>
            <p class="text-muted mb-0">Gestión y visualización de reportes OSINT</p>
        </div>
        <div>
            <button class="btn btn-primary" onclick="refreshReports()">
                <i class="fas fa-sync-alt me-1"></i>
                Actualizar
            </button>
            <a href="/analyze" class="btn btn-success">
                <i class="fas fa-plus me-1"></i>
                Nuevo Análisis
            </a>
        </div>
    </div>

    <!-- Filtros y controles -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" class="form-control" id="searchDomain" placeholder="Buscar por dominio...">
            </div>
        </div>
        <div class="col-md-3">
            <select class="form-select" id="filterRisk">
                <option value="">Todos los niveles de riesgo</option>
                <option value="Alto">Alto</option>
                <option value="Medio">Medio</option>
                <option value="Bajo">Bajo</option>
            </select>
        </div>
        <div class="col-md-3">
            <select class="form-select" id="sortBy">
                <option value="date_desc">Más recientes primero</option>
                <option value="date_asc">Más antiguos primero</option>
                <option value="domain_asc">Dominio (A-Z)</option>
                <option value="risk_desc">Riesgo (Alto a Bajo)</option>
            </select>
        </div>
        <div class="col-md-2">
            <div class="text-end">
                <span class="badge bg-info">{{ reports|length }} informes</span>
            </div>
        </div>
    </div>

    <!-- Tabla de informes -->
    <div class="card">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th><i class="fas fa-globe me-1"></i>Dominio</th>
                            <th><i class="fas fa-calendar me-1"></i>Fecha</th>
                            <th><i class="fas fa-shield-alt me-1"></i>Riesgo</th>
                            <th><i class="fas fa-chart-bar me-1"></i>Hallazgos</th>
                            <th><i class="fas fa-cogs me-1"></i>Acciones</th>
                        </tr>
                    </thead>
                    <tbody id="reportsTableBody">
                        {% for report in reports %}
                        <tr data-domain="{{ report.domain }}" data-risk="{{ report.risk_level }}" data-date="{{ report.date }}">
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-globe text-primary me-2"></i>
                                    <div>
                                        <strong>{{ report.domain }}</strong>
                                        {% if report.has_html %}
                                        <br><small class="text-muted">Con visualización</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <small>{{ report.date }}</small>
                            </td>
                            <td>
                                {% if report.risk_level == 'Alto' %}
                                    <span class="badge bg-danger">Alto</span>
                                {% elif report.risk_level == 'Medio' %}
                                    <span class="badge bg-warning">Medio</span>
                                {% else %}
                                    <span class="badge bg-success">Bajo</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-info">{{ report.findings }}</span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    {% if report.has_html %}
                                    <a href="/visualize/{{ report.filename }}" class="btn btn-outline-primary" title="Ver visualización">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% endif %}
                                    <button class="btn btn-outline-info" onclick="viewDetails('{{ report.filename }}')" title="Ver detalles">
                                        <i class="fas fa-info-circle"></i>
                                    </button>
                                    <button class="btn btn-outline-success" onclick="downloadReport('{{ report.filename }}')" title="Descargar">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="copyLink('{{ report.filename }}')" title="Copiar enlace">
                                        <i class="fas fa-link"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    {% if not reports %}
    <div class="text-center py-5">
        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">No hay informes disponibles</h4>
        <p class="text-muted">Realiza tu primer análisis OSINT para generar informes.</p>
        <a href="/analyze" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            Comenzar Análisis
        </a>
    </div>
    {% endif %}
</div>

<!-- Modal para detalles -->
<div class="modal fade" id="detailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-file-alt me-2"></i>
                    Detalles del Informe
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="detailsContent">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p class="mt-2">Cargando...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let detailsModal;

document.addEventListener('DOMContentLoaded', function() {
    detailsModal = new bootstrap.Modal(document.getElementById('detailsModal'));
    
    // Event listeners para filtros
    document.getElementById('searchDomain').addEventListener('input', filterReports);
    document.getElementById('filterRisk').addEventListener('change', filterReports);
    document.getElementById('sortBy').addEventListener('change', sortReports);
});

function filterReports() {
    const searchTerm = document.getElementById('searchDomain').value.toLowerCase();
    const riskFilter = document.getElementById('filterRisk').value;
    const rows = document.querySelectorAll('#reportsTableBody tr');
    
    rows.forEach(row => {
        const domain = row.dataset.domain.toLowerCase();
        const risk = row.dataset.risk;
        
        const matchesSearch = domain.includes(searchTerm);
        const matchesRisk = !riskFilter || risk === riskFilter;
        
        row.style.display = (matchesSearch && matchesRisk) ? '' : 'none';
    });
}

function sortReports() {
    const sortBy = document.getElementById('sortBy').value;
    const tbody = document.getElementById('reportsTableBody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    rows.sort((a, b) => {
        switch (sortBy) {
            case 'date_desc':
                return new Date(b.dataset.date) - new Date(a.dataset.date);
            case 'date_asc':
                return new Date(a.dataset.date) - new Date(b.dataset.date);
            case 'domain_asc':
                return a.dataset.domain.localeCompare(b.dataset.domain);
            case 'risk_desc':
                const riskOrder = { 'Alto': 3, 'Medio': 2, 'Bajo': 1 };
                return (riskOrder[b.dataset.risk] || 0) - (riskOrder[a.dataset.risk] || 0);
            default:
                return 0;
        }
    });
    
    rows.forEach(row => tbody.appendChild(row));
}

function refreshReports() {
    location.reload();
}

function downloadReport(filename) {
    const link = document.createElement('a');
    link.href = '/api/report/' + filename;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function viewDetails(filename) {
    document.getElementById('detailsContent').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><p class="mt-2">Cargando detalles...</p></div>';
    
    detailsModal.show();
    
    fetch('/api/report/' + filename)
        .then(response => response.json())
        .then(data => {
            displayReportDetails(data);
        })
        .catch(error => {
            document.getElementById('detailsContent').innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>Error cargando los detalles: ' + error.message + '</div>';
        });
}

function displayReportDetails(data) {
    const executive = data.executive_summary || {};
    const detailed = data.detailed_findings || {};
    
    let html = '<div class="row">';
    html += '<div class="col-md-6">';
    html += '<h6><i class="fas fa-info-circle me-1"></i> Resumen Ejecutivo</h6>';
    html += '<table class="table table-sm">';
    html += '<tr><th>Dominio:</th><td>' + (executive.domain || 'N/A') + '</td></tr>';
    html += '<tr><th>Fecha:</th><td>' + (executive.analysis_date || 'N/A') + '</td></tr>';
    html += '<tr><th>Nivel de Riesgo:</th><td>';
    
    const riskLevel = executive.risk_level || 'N/A';
    const badgeClass = riskLevel === 'Alto' ? 'danger' : (riskLevel === 'Medio' ? 'warning' : 'success');
    html += '<span class="badge bg-' + badgeClass + '">' + riskLevel + '</span>';
    html += '</td></tr>';
    html += '<tr><th>Total Hallazgos:</th><td>' + (executive.total_findings || 0) + '</td></tr>';
    html += '</table>';
    html += '</div>';
    
    html += '<div class="col-md-6">';
    html += '<h6><i class="fas fa-network-wired me-1"></i> Información Técnica</h6>';
    html += '<table class="table table-sm">';
    
    const reconnaissance = detailed.reconnaissance || {};
    const dnsInfo = reconnaissance.dns_info || {};
    const sslInfo = reconnaissance.ssl_info || {};
    
    html += '<tr><th>IP:</th><td>' + (dnsInfo.ip_address || 'N/A') + '</td></tr>';
    html += '<tr><th>SSL:</th><td>' + (sslInfo.ssl_version || 'N/A') + '</td></tr>';
    
    const osint = detailed.osint || {};
    const subdomains = osint.subdomains || [];
    html += '<tr><th>Subdominios:</th><td>' + subdomains.length + '</td></tr>';
    html += '</table>';
    html += '</div>';
    html += '</div>';
    
    document.getElementById('detailsContent').innerHTML = html;
}

function copyLink(filename) {
    const link = window.location.origin + '/visualize/' + filename;
    navigator.clipboard.writeText(link).then(() => {
        // Mostrar notificación simple
        alert('Enlace copiado al portapapeles');
    });
}
</script>
{% endblock %} 