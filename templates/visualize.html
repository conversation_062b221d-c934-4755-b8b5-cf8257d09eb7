{% extends "base.html" %}

{% block title %}Visualización - {{ report.executive_summary.domain }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="text-white mb-0 fade-in-up">
            <i class="fas fa-chart-bar me-3"></i>
            Visualización: {{ report.executive_summary.domain }}
        </h1>
        <p class="text-white-50 mt-2 fade-in-up">
            Análisis realizado el {{ report.executive_summary.analysis_date[:16] if report.executive_summary.analysis_date else 'N/A' }}
        </p>
    </div>
</div>

<!-- Resumen ejecutivo visual -->
<div class="row mb-4 fade-in-up">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card {{ 'border-danger' if report.executive_summary.risk_level == 'Alto' else 'border-warning' if report.executive_summary.risk_level == 'Medio' else 'border-success' }}">
            <div class="card-body text-center">
                <i class="fas fa-shield-alt fa-3x mb-3 text-{{ 'danger' if report.executive_summary.risk_level == '<PERSON>' else 'warning' if report.executive_summary.risk_level == 'Medio' else 'success' }}"></i>
                <h4 class="text-{{ 'danger' if report.executive_summary.risk_level == 'Alto' else 'warning' if report.executive_summary.risk_level == 'Medio' else 'success' }}">
                    {{ report.executive_summary.risk_level }}
                </h4>
                <small class="text-muted">Nivel de Riesgo</small>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-search fa-3x mb-3 text-info"></i>
                <h4 class="text-info">{{ report.executive_summary.total_findings or 0 }}</h4>
                <small class="text-muted">Total Hallazgos</small>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-sitemap fa-3x mb-3 text-primary"></i>
                <h4 class="text-primary">{{ (report.detailed_findings.osint.all_subdomains|length if report.detailed_findings.osint.all_subdomains else 0) or (report.detailed_findings.osint.subdomains|length if report.detailed_findings.osint.subdomains else 0) }}</h4>
                <small class="text-muted">Subdominios</small>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-server fa-3x mb-3 text-secondary"></i>
                <h4 class="text-secondary">
                    {% if report.detailed_findings and report.detailed_findings.reconnaissance and report.detailed_findings.reconnaissance.dns_info and report.detailed_findings.reconnaissance.dns_info.ip_address %}
                        {{ report.detailed_findings.reconnaissance.dns_info.ip_address }}
                    {% else %}
                        N/A
                    {% endif %}
                </h4>
                <small class="text-muted">Dirección IP</small>
            </div>
        </div>
    </div>
</div>

<!-- Gráficos principales -->
<div class="row mb-4">
    <!-- Gráfico de cabeceras de seguridad -->
    <div class="col-lg-6 mb-4 fade-in-up">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    Cabeceras de Seguridad
                </h5>
            </div>
            <div class="card-body">
                {% if charts.security_headers %}
                    <div id="securityHeadersChart"></div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-exclamation-circle fa-2x mb-2"></i>
                        <p>No hay datos de cabeceras de seguridad disponibles</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Gráfico de nivel de riesgo -->
    <div class="col-lg-6 mb-4 fade-in-up">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    Distribución de Riesgos
                </h5>
            </div>
            <div class="card-body">
                {% if charts.risk_level %}
                    <div id="riskLevelChart"></div>
                {% else %}
                    <canvas id="riskCanvas" width="400" height="300"></canvas>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Información técnica detallada -->
<div class="row mb-4">
    <div class="col-lg-6 fade-in-up">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-server me-2"></i>
                    Información Técnica
                </h5>
            </div>
            <div class="card-body">
                <div class="tech-info">
                    {% if report.detailed_findings and report.detailed_findings.reconnaissance %}
                        {% set dns_info = report.detailed_findings.reconnaissance.dns_info if report.detailed_findings.reconnaissance.dns_info else {} %}
                        {% set ssl_info = report.detailed_findings.reconnaissance.ssl_info if report.detailed_findings.reconnaissance.ssl_info else {} %}
                        {% set tech_info = report.detailed_findings.reconnaissance.technology if report.detailed_findings.reconnaissance.technology else {} %}
                    {% else %}
                        {% set dns_info = {} %}
                        {% set ssl_info = {} %}
                        {% set tech_info = {} %}
                    {% endif %}
                    
                    <div class="info-group mb-3">
                        <h6><i class="fas fa-network-wired me-1 text-primary"></i> DNS</h6>
                        <table class="table table-sm">
                            <tr>
                                <th width="40%">IP Principal:</th>
                                <td>{{ dns_info.ip_address or 'N/A' }}</td>
                            </tr>
                            <tr>
                                <th>Registros A:</th>
                                <td>
                                    {% if dns_info.a_records %}
                                        {% for record in dns_info.a_records %}
                                            <span class="badge bg-secondary me-1">{{ record }}</span>
                                        {% endfor %}
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Servidores de Correo:</th>
                                <td>
                                    {% if dns_info.mx_records %}
                                        {% for record in dns_info.mx_records %}
                                            <span class="badge bg-info me-1">{{ record }}</span>
                                        {% endfor %}
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>

                    <div class="info-group mb-3">
                        <h6><i class="fas fa-lock me-1 text-success"></i> SSL/TLS</h6>
                        <table class="table table-sm">
                            <tr>
                                <th width="40%">Versión SSL:</th>
                                <td>{{ ssl_info.ssl_version or 'N/A' }}</td>
                            </tr>
                            <tr>
                                <th>Cifrado:</th>
                                <td>{{ ssl_info.cipher_suite or 'N/A' }}</td>
                            </tr>
                            <tr>
                                <th>Emisor Certificado:</th>
                                <td>{{ ssl_info.issuer or 'N/A' }}</td>
                            </tr>
                            <tr>
                                <th>Expiración:</th>
                                <td>{{ ssl_info.expiry_date or 'N/A' }}</td>
                            </tr>
                        </table>
                    </div>

                    <div class="info-group">
                        <h6><i class="fas fa-code me-1 text-warning"></i> Tecnologías</h6>
                        <table class="table table-sm">
                            <tr>
                                <th width="40%">Servidor Web:</th>
                                <td>{{ tech_info.web_server or 'N/A' }}</td>
                            </tr>
                            <tr>
                                <th>Framework:</th>
                                <td>{{ tech_info.framework or 'N/A' }}</td>
                            </tr>
                            <tr>
                                <th>Lenguaje:</th>
                                <td>{{ tech_info.programming_language or 'N/A' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 fade-in-up">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-sitemap me-2"></i>
                    Subdominios Encontrados
                </h5>
            </div>
            <div class="card-body">
                {% if report.detailed_findings.osint.all_subdomains or report.detailed_findings.osint.subdomains %}
                    <div class="subdomains-list" style="max-height: 400px; overflow-y: auto;">
                        {% for subdomain in (report.detailed_findings.osint.all_subdomains or report.detailed_findings.osint.subdomains or []) %}
                            <div class="subdomain-item mb-2 p-2 rounded" style="background: rgba(0,0,0,0.05);">
                                <strong>{{ subdomain }}</strong>
                                <div class="btn-group btn-group-sm ms-2" role="group">
                                    <button class="btn btn-outline-primary btn-sm" onclick="checkSubdomain('{{ subdomain }}')" title="Verificar estado">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" onclick="copySubdomain('{{ subdomain }}')" title="Copiar">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-search fa-2x mb-2"></i>
                        <p>No se encontraron subdominios</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Tabla de cabeceras de seguridad -->
<div class="row mb-4 fade-in-up">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    Análisis Detallado de Seguridad
                </h5>
            </div>
            <div class="card-body">
                {% if report.detailed_findings.vulnerabilities.security_headers %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Cabecera de Seguridad</th>
                                <th>Estado</th>
                                <th>Riesgo</th>
                                <th>Descripción</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for header, status in report.detailed_findings.vulnerabilities.security_headers.items() %}
                            <tr>
                                <td><strong>{{ header }}</strong></td>
                                <td>
                                    <span class="badge bg-{{ 'success' if status != 'Ausente' else 'danger' }}">
                                        {{ status }}
                                    </span>
                                </td>
                                <td>
                                    {% if status == 'Ausente' %}
                                        <span class="badge bg-{{ 'danger' if header in ['Content-Security-Policy', 'X-Frame-Options'] else 'warning' }}">
                                            {{ 'Alto' if header in ['Content-Security-Policy', 'X-Frame-Options'] else 'Medio' }}
                                        </span>
                                    {% else %}
                                        <span class="badge bg-success">Bajo</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {% if header == 'Content-Security-Policy' %}
                                            Previene ataques XSS y inyección de código
                                        {% elif header == 'X-Frame-Options' %}
                                            Previene ataques de clickjacking
                                        {% elif header == 'Strict-Transport-Security' %}
                                            Fuerza conexiones HTTPS seguras
                                        {% elif header == 'X-Content-Type-Options' %}
                                            Previene ataques MIME sniffing
                                        {% elif header == 'Referrer-Policy' %}
                                            Controla información de referencia
                                        {% else %}
                                            Cabecera de seguridad adicional
                                        {% endif %}
                                    </small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <p>No hay información de cabeceras de seguridad disponible</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recomendaciones -->
{% if report.recommendations %}
<div class="row mb-4 fade-in-up">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    Recomendaciones de Seguridad
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for recommendation in report.recommendations %}
                    <div class="col-lg-6 mb-3">
                        <div class="recommendation-card p-3 rounded" style="background: rgba(0,0,0,0.05); border-left: 4px solid {{ '#e74c3c' if recommendation.priority == 'Alta' else '#f39c12' if recommendation.priority == 'Media' else '#27ae60' }};">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="mb-0">{{ recommendation.title }}</h6>
                                <span class="badge bg-{{ 'danger' if recommendation.priority == 'Alta' else 'warning' if recommendation.priority == 'Media' else 'success' }}">
                                    {{ recommendation.priority }}
                                </span>
                            </div>
                            <p class="text-muted mb-2">{{ recommendation.description }}</p>
                            {% if recommendation.impact %}
                                <small class="text-info">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Impacto: {{ recommendation.impact }}
                                </small>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Acciones -->
<div class="row mb-4 fade-in-up">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <h6 class="mb-3">Acciones Disponibles</h6>
                <div class="btn-group" role="group">
                    <a href="/api/report/{{ filename }}" class="btn btn-primary" download>
                        <i class="fas fa-download me-1"></i>
                        Descargar JSON
                    </a>
                    <button class="btn btn-info" onclick="printReport()">
                        <i class="fas fa-print me-1"></i>
                        Imprimir
                    </button>
                    <button class="btn btn-success" onclick="shareReport()">
                        <i class="fas fa-share me-1"></i>
                        Compartir
                    </button>
                    <a href="{{ url_for('reports_page') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Volver a Informes
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Renderizar gráficos de Plotly si están disponibles
    {% if charts.security_headers %}
        const securityData = {{ charts.security_headers|safe }};
        Plotly.newPlot('securityHeadersChart', securityData.data, securityData.layout, {responsive: true});
    {% endif %}
    
    {% if charts.risk_level %}
        const riskData = {{ charts.risk_level|safe }};
        Plotly.newPlot('riskLevelChart', riskData.data, riskData.layout, {responsive: true});
    {% else %}
        // Crear gráfico de riesgo alternativo con Chart.js
        createRiskChart();
    {% endif %}
    
    // Aplicar animaciones
    const elements = document.querySelectorAll('.fade-in-up');
    elements.forEach((el, index) => {
        el.style.animationDelay = (index * 0.1) + 's';
    });
});

function createRiskChart() {
    const ctx = document.getElementById('riskCanvas');
    if (!ctx) return;
    
    const riskLevel = '{{ report.executive_summary.risk_level }}';
    const colors = {
        'Alto': '#e74c3c',
        'Medio': '#f39c12',
        'Bajo': '#27ae60'
    };
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Riesgo Detectado', 'Seguridad'],
            datasets: [{
                data: riskLevel === 'Alto' ? [80, 20] : riskLevel === 'Medio' ? [50, 50] : [20, 80],
                backgroundColor: [colors[riskLevel] || '#6c757d', '#ecf0f1'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function checkSubdomain(subdomain) {
    // Verificar estado del subdominio
    const btn = event.target.closest('button');
    const originalContent = btn.innerHTML;
    
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;
    
    // Simular verificación (en una implementación real, haríamos una petición al servidor)
    setTimeout(() => {
        const isActive = Math.random() > 0.3; // 70% probabilidad de estar activo
        btn.innerHTML = `<i class="fas fa-${isActive ? 'check text-success' : 'times text-danger'}"></i>`;
        
        setTimeout(() => {
            btn.innerHTML = originalContent;
            btn.disabled = false;
        }, 2000);
    }, 1000);
}

function copySubdomain(subdomain) {
    navigator.clipboard.writeText(subdomain).then(() => {
        const btn = event.target.closest('button');
        const originalContent = btn.innerHTML;
        
        btn.innerHTML = '<i class="fas fa-check text-success"></i>';
        
        setTimeout(() => {
            btn.innerHTML = originalContent;
        }, 1000);
    });
}

function printReport() {
    window.print();
}

function shareReport() {
    const url = window.location.href;
    
    if (navigator.share) {
        navigator.share({
            title: 'Informe OSINT - {{ report.executive_summary.domain }}',
            text: 'Análisis de seguridad OSINT realizado',
            url: url
        });
    } else {
        // Fallback para navegadores que no soportan Web Share API
        navigator.clipboard.writeText(url).then(() => {
            alert('Enlace copiado al portapapeles');
        });
    }
}
</script>

{% block extra_css %}
<style>
@media print {
    .btn, .navbar, .footer {
        display: none !important;
    }
    
    .card {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
    
    body {
        background: white !important;
    }
    
    .text-white, .text-white-50 {
        color: black !important;
    }
}

.info-group {
    border-left: 3px solid #3498db;
    padding-left: 1rem;
}

.recommendation-card {
    transition: transform 0.2s ease;
}

.recommendation-card:hover {
    transform: translateY(-2px);
}

.subdomain-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s ease;
}

.subdomain-item:hover {
    background: rgba(0,0,0,0.1) !important;
}

.tech-info .table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.tech-info .table td {
    border-top: 1px solid #dee2e6;
}
</style>
{% endblock %}
{% endblock %} 