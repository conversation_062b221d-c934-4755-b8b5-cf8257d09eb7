#!/usr/bin/env python3
"""
Script de prueba para generar análisis técnico avanzado
"""

import sys
import os
import json
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from osint_framework import OSINTAnalyzer

def test_advanced_technical_analysis():
    """Prueba el análisis técnico avanzado"""
    print("🔬 Probando análisis técnico avanzado...")
    
    # Crear analizador con datos de prueba
    analyzer = OSINTAnalyzer("advanced-test.com", authorized=True)
    
    # Simular datos SSL/TLS avanzados
    analyzer.results['reconnaissance'] = {
        'ssl_info': {
            'ssl_version': 'TLSv1.3',
            'cipher_suite': 'TLS_AES_256_GCM_SHA384',
            'cipher_bits': 256,
            'cipher_version': 'TLSv1.3',
            'cipher_security': 'Seguro',
            'public_key_algorithm': 'RSA',
            'public_key_size': 2048,
            'fingerprint_sha1': 'A1:B2:C3:D4:E5:F6:07:08:09:0A:1B:2C:3D:4E:5F:60:71:82:93:A4',
            'fingerprint_sha256': 'A1B2C3D4E5F6070809A1B2C3D4E5F6070809A1B2C3D4E5F6070809A1B2C3D4E5F607',
            'issuer': {
                'organizationName': 'Let\'s Encrypt',
                'countryName': 'US'
            },
            'subject': {
                'commonName': 'advanced-test.com',
                'organizationName': 'Test Organization'
            },
            'expiry_date': 'Dec 31 23:59:59 2025 GMT',
            'start_date': 'Jan 01 00:00:00 2024 GMT',
            'san': [
                'advanced-test.com',
                'www.advanced-test.com',
                'api.advanced-test.com',
                'cdn.advanced-test.com',
                'mail.advanced-test.com'
            ],
            'serial_number': '123456789ABCDEF',
            'signature_algorithm': 'sha256WithRSAEncryption',
            'version': 3,
            'extensions': {
                'basicConstraints': {'critical': True, 'oid': '2.5.29.19'},
                'keyUsage': {'critical': True, 'oid': '2.5.29.15'},
                'extendedKeyUsage': {'critical': False, 'oid': '2.5.29.37'},
                'subjectAltName': {'critical': False, 'oid': '2.5.29.17'},
                'authorityKeyIdentifier': {'critical': False, 'oid': '2.5.29.35'}
            },
            'certificate_chain_length': 3,
            'ocsp_stapling': True,
            'sct_extension': True
        },
        'port_analysis': {
            'open_ports': [22, 80, 443, 8080, 3306, 6379],
            'categorized_ports': {
                'web': [80, 443, 8080],
                'remote': [22],
                'database': [3306, 6379]
            },
            'service_detection': {
                '22': 'SSH - OpenSSH 8.9p1 Ubuntu',
                '80': 'HTTP - nginx/1.20.2',
                '443': 'HTTPS/SSL',
                '8080': 'HTTP - Apache Tomcat/9.0.65',
                '3306': 'Database - MySQL',
                '6379': 'Database - Redis'
            },
            'security_analysis': {
                'risk_level': 'Alto',
                'concerns': [
                    'Puertos de alto riesgo abiertos: [22, 3306, 6379]',
                    'Bases de datos potencialmente expuestas: [3306, 6379]'
                ],
                'recommendations': [
                    'Cerrar puertos innecesarios o restringir acceso',
                    'Restringir acceso a bases de datos solo a IPs autorizadas'
                ]
            },
            'total_open': 6
        },
        'dns_info': {
            'ip_address': '*************',
            'a_records': ['*************', '*************'],
            'aaaa_records': ['2001:db8::1', '2001:db8::2'],
            'mx_records': ['10 mail.advanced-test.com', '20 backup-mail.advanced-test.com'],
            'txt_records': [
                'v=spf1 include:_spf.google.com ~all',
                'google-site-verification=abcdef123456',
                'v=DMARC1; p=quarantine; rua=mailto:<EMAIL>'
            ],
            'ns_records': ['ns1.advanced-test.com', 'ns2.advanced-test.com'],
            'cname_records': ['www.advanced-test.com'],
            'soa_record': {
                'mname': 'ns1.advanced-test.com',
                'rname': 'admin.advanced-test.com',
                'serial': 2024010101,
                'refresh': 3600,
                'retry': 1800,
                'expire': 604800,
                'minimum': 86400
            },
            'srv_records': [],
            'ptr_records': [],
            'caa_records': ['0 issue "letsencrypt.org"', '0 iodef "mailto:<EMAIL>"'],
            'dmarc_record': 'v=DMARC1; p=quarantine; rua=mailto:<EMAIL>; ruf=mailto:<EMAIL>; sp=quarantine; adkim=r; aspf=r',
            'spf_record': 'v=spf1 include:_spf.google.com include:mailgun.org ~all',
            'dkim_records': [
                {
                    'selector': 'google',
                    'record': 'v=DKIM1; k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC...'
                },
                {
                    'selector': 'mailgun',
                    'record': 'v=DKIM1; k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQD...'
                }
            ],
            'dns_security': {
                'email_security_score': 100,
                'dns_security_score': 85,
                'recommendations': [],
                'findings': [
                    'SPF configurado',
                    'DMARC configurado',
                    'DKIM configurado (2 selectores)',
                    'CAA configurado',
                    'IPv6 configurado',
                    'Reverse DNS configurado'
                ],
                'level': 'Excelente',
                'total_score': 92.5
            },
            'reverse_dns': 'advanced-test.com',
            'dns_propagation': {},
            'dns_performance': {}
        },
        'technology': {
            'web_server': 'nginx/1.20.2',
            'detected': ['WordPress', 'jQuery', 'Bootstrap', 'Cloudflare'],
            'header_technologies': {
                'X-Powered-By': 'PHP/8.1.0',
                'Server': 'nginx/1.20.2'
            },
            'javascript_frameworks': ['jQuery 3.6.0', 'Bootstrap 5.1.3'],
            'framework': 'WordPress',
            'programming_language': 'PHP'
        },
        'response_info': {
            'status_code': 200,
            'content_type': 'text/html; charset=UTF-8',
            'content_length': '45678',
            'server': 'nginx/1.20.2',
            'last_modified': 'Wed, 01 Jan 2024 12:00:00 GMT',
            'etag': '"abc123-def456"',
            'final_url': 'https://advanced-test.com/',
            'cookies': [
                {
                    'name': 'session_id',
                    'secure': True,
                    'httponly': True,
                    'samesite': 'Strict'
                },
                {
                    'name': 'analytics',
                    'secure': False,
                    'httponly': False,
                    'samesite': None
                }
            ]
        }
    }
    
    # Simular vulnerabilidades
    analyzer.results['vulnerabilities'] = {
        'security_headers': {
            'Content-Security-Policy': 'default-src \'self\'; script-src \'self\' \'unsafe-inline\'',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'X-Frame-Options': 'SAMEORIGIN',
            'X-Content-Type-Options': 'nosniff',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'X-XSS-Protection': '1; mode=block',
            'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',
            'Expect-CT': 'Ausente',
            'X-Permitted-Cross-Domain-Policies': 'Ausente'
        }
    }
    
    # Simular OSINT
    analyzer.results['osint'] = {
        'subdomains': [
            'www.advanced-test.com',
            'api.advanced-test.com',
            'admin.advanced-test.com',
            'dev.advanced-test.com',
            'staging.advanced-test.com',
            'mail.advanced-test.com',
            'cdn.advanced-test.com',
            'blog.advanced-test.com'
        ],
        'subdomain_sources': {
            'certificate_transparency': ['www.advanced-test.com', 'api.advanced-test.com', 'mail.advanced-test.com'],
            'dns_bruteforce': ['admin.advanced-test.com', 'dev.advanced-test.com'],
            'commoncrawl': ['blog.advanced-test.com', 'cdn.advanced-test.com']
        }
    }
    
    # Generar recomendaciones
    recommendations = analyzer.generate_recommendations()
    
    # Crear reporte completo
    analyzer.results['executive_summary'] = {
        'domain': 'advanced-test.com',
        'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'risk_level': 'Medio',
        'total_findings': len(recommendations)
    }
    
    # Guardar reporte de prueba
    report_filename = f"advanced_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    with open(report_filename, 'w', encoding='utf-8') as f:
        json.dump(analyzer.results, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ Reporte de análisis avanzado guardado: {report_filename}")
    print(f"🌐 Visualizar en: http://localhost:8080/visualize/{report_filename}")
    
    # Mostrar resumen técnico
    print(f"\n📊 Resumen del Análisis Técnico Avanzado:")
    print(f"   🔒 SSL/TLS: {analyzer.results['reconnaissance']['ssl_info']['ssl_version']} con {analyzer.results['reconnaissance']['ssl_info']['cipher_suite']}")
    print(f"   🔌 Puertos abiertos: {len(analyzer.results['reconnaissance']['port_analysis']['open_ports'])}")
    print(f"   🌐 Puntuación DNS: {analyzer.results['reconnaissance']['dns_info']['dns_security']['total_score']:.1f}%")
    print(f"   📧 Seguridad Email: SPF ✓, DMARC ✓, DKIM ✓")
    print(f"   🛡️  Cabeceras de seguridad: {len([h for h, v in analyzer.results['vulnerabilities']['security_headers'].items() if v != 'Ausente'])}/9 configuradas")
    print(f"   🎯 Subdominios encontrados: {len(analyzer.results['osint']['subdomains'])}")
    print(f"   💡 Recomendaciones generadas: {len(recommendations)}")
    
    return report_filename

if __name__ == "__main__":
    test_advanced_technical_analysis()
