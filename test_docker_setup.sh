#!/bin/bash
# Script to test OSINTdesk Docker setup

set -e
echo "======================================================================"
echo "              OSINTdesk Docker Setup Verification Tool                "
echo "======================================================================"

# Check Docker installation
echo "[1/5] Checking Docker installation..."
if ! command -v docker &> /dev/null; then
    echo "❌ ERROR: Docker is not installed or not in PATH"
    echo "Please install Docker first: https://docs.docker.com/get-docker/"
    exit 1
else
    DOCKER_VERSION=$(docker --version)
    echo "✅ Docker is installed: $DOCKER_VERSION"
fi

# Check Docker Compose installation
echo "[2/5] Checking Docker Compose installation..."
if ! command -v docker-compose &> /dev/null; then
    echo "❌ ERROR: Docker Compose is not installed or not in PATH"
    echo "Please install Docker Compose first: https://docs.docker.com/compose/install/"
    exit 1
else
    COMPOSE_VERSION=$(docker-compose --version)
    echo "✅ Docker Compose is installed: $COMPOSE_VERSION"
fi

# Check for required files
echo "[3/5] Checking for required project files..."
MISSING_FILES=0

for file in "Dockerfile" "docker-compose.yml" "docker-entrypoint.sh" "requirements.txt"; do
    if [ ! -f "$file" ]; then
        echo "❌ Missing required file: $file"
        MISSING_FILES=$((MISSING_FILES+1))
    else
        echo "✅ Found file: $file"
    fi
done

if [ $MISSING_FILES -gt 0 ]; then
    echo "❌ ERROR: $MISSING_FILES required files are missing"
    exit 1
fi

# Check for .env file or create it
echo "[4/5] Checking for .env file..."
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        echo "⚠️ No .env file found, but .env.example exists."
        echo "   Creating .env from .env.example (you'll need to add your API keys later)"
        cp .env.example .env
    else
        echo "⚠️ No .env or .env.example file found."
        echo "   Creating basic .env file (you'll need to add your API keys later)"
        cat > .env << EOL
# OSINTdesk API Keys
SHODAN_API_KEY=
CENSYS_API_ID=
CENSYS_API_SECRET=
VIRUSTOTAL_API_KEY=
HUNTER_API_KEY=
SECURITY_TRAILS_API_KEY=
MAXMIND_LICENSE_KEY=
EOL
    fi
    echo "✅ Created .env file"
else
    echo "✅ Found .env file"
fi

# Try building the Docker image
echo "[5/5] Testing Docker build..."
echo "Building OSINTdesk Docker image (this may take a few minutes)..."
if docker-compose build --no-cache; then
    echo "✅ Docker image built successfully"
else
    echo "❌ ERROR: Docker build failed"
    exit 1
fi

echo "======================================================================"
echo "                     All checks completed!                            "
echo "======================================================================"
echo ""
echo "Your OSINTdesk Docker setup appears to be configured correctly."
echo ""
echo "To start OSINTdesk, run:"
echo "  docker-compose up -d"
echo ""
echo "Then access the web interface at:"
echo "  http://localhost:5001"
echo ""
echo "Don't forget to add your API keys to the .env file for full functionality!"
echo "======================================================================" 