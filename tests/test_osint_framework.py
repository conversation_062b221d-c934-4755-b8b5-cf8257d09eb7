#!/usr/bin/env python3
"""
Test Suite for OSINT Framework
==============================

Comprehensive test suite for the OSINT Security Analysis Framework.
Tests cover all major functionality including DNS analysis, SSL verification,
subdomain enumeration, and report generation.
"""

import pytest
import unittest
from unittest.mock import Mock, patch, MagicMock
import json
import tempfile
import os
import sys

# Add the parent directory to the path to import the framework
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from osint_framework import OSINTAnalyzer
from config import Config


class TestOSINTAnalyzer(unittest.TestCase):
    """Test cases for OSINTAnalyzer class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_domain = "example.com"
        self.analyzer = OSINTAnalyzer(self.test_domain, authorized=True)
    
    def test_domain_cleaning(self):
        """Test domain input cleaning and validation"""
        test_cases = [
            ("https://example.com", "example.com"),
            ("http://www.example.com", "example.com"),
            ("www.example.com/", "example.com"),
            ("EXAMPLE.COM", "example.com"),
        ]
        
        for input_domain, expected in test_cases:
            analyzer = OSINTAnalyzer(input_domain, authorized=True)
            self.assertEqual(analyzer.domain, expected)
    
    def test_initialization(self):
        """Test proper initialization of analyzer"""
        self.assertEqual(self.analyzer.domain, self.test_domain)
        self.assertTrue(self.analyzer.authorized)
        self.assertIsNotNone(self.analyzer.results)
        self.assertIn('domain', self.analyzer.results)
        self.assertIn('timestamp', self.analyzer.results)
    
    @patch('socket.gethostbyname')
    @patch('dns.resolver.Resolver')
    def test_basic_domain_info(self, mock_resolver, mock_gethostbyname):
        """Test basic domain information gathering"""
        # Mock DNS resolution
        mock_gethostbyname.return_value = "*************"
        
        # Mock DNS resolver
        mock_resolver_instance = Mock()
        mock_resolver.return_value = mock_resolver_instance
        
        # Mock A records
        mock_a_record = Mock()
        mock_a_record.__str__ = Mock(return_value="*************")
        mock_resolver_instance.resolve.return_value = [mock_a_record]
        
        # Run the test
        self.analyzer.basic_domain_info()
        
        # Verify results
        self.assertIn('dns_info', self.analyzer.results['reconnaissance'])
        dns_info = self.analyzer.results['reconnaissance']['dns_info']
        self.assertEqual(dns_info['ip_address'], "*************")
    
    @patch('ssl.create_default_context')
    @patch('socket.create_connection')
    def test_ssl_analysis(self, mock_connection, mock_ssl_context):
        """Test SSL/TLS analysis functionality"""
        # Mock SSL context and socket
        mock_context = Mock()
        mock_ssl_context.return_value = mock_context
        
        mock_socket = Mock()
        mock_connection.return_value.__enter__ = Mock(return_value=mock_socket)
        mock_connection.return_value.__exit__ = Mock(return_value=None)
        
        mock_ssl_socket = Mock()
        mock_context.wrap_socket.return_value.__enter__ = Mock(return_value=mock_ssl_socket)
        mock_context.wrap_socket.return_value.__exit__ = Mock(return_value=None)
        
        # Mock certificate data
        mock_cert = {
            'issuer': [['organizationName', 'Test CA']],
            'subject': [['commonName', 'example.com']],
            'notAfter': 'Dec 31 23:59:59 2024 GMT',
            'notBefore': 'Jan 1 00:00:00 2024 GMT',
            'serialNumber': '123456789',
            'subjectAltName': [('DNS', 'example.com'), ('DNS', 'www.example.com')]
        }
        
        mock_ssl_socket.getpeercert.return_value = mock_cert
        mock_ssl_socket.version.return_value = 'TLSv1.3'
        mock_ssl_socket.cipher.return_value = ('ECDHE-RSA-AES256-GCM-SHA384', 'TLSv1.3', 256)
        
        # Run the test
        result = self.analyzer.ssl_analysis()
        
        # Verify results
        self.assertIsNotNone(result)
        self.assertIn('ssl_info', self.analyzer.results['reconnaissance'])
    
    @patch('requests.get')
    def test_robots_sitemap_analysis(self, mock_get):
        """Test robots.txt and sitemap.xml analysis"""
        # Mock robots.txt response
        mock_robots_response = Mock()
        mock_robots_response.status_code = 200
        mock_robots_response.text = """User-agent: *
Disallow: /admin/
Disallow: /private/
Sitemap: https://example.com/sitemap.xml"""
        
        # Mock sitemap.xml response
        mock_sitemap_response = Mock()
        mock_sitemap_response.status_code = 200
        mock_sitemap_response.text = """<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <url><loc>https://example.com/</loc></url>
    <url><loc>https://example.com/about</loc></url>
</urlset>"""
        
        # Configure mock to return different responses based on URL
        def side_effect(url, **kwargs):
            if 'robots.txt' in url:
                return mock_robots_response
            elif 'sitemap.xml' in url:
                return mock_sitemap_response
            else:
                mock_404 = Mock()
                mock_404.status_code = 404
                return mock_404
        
        mock_get.side_effect = side_effect
        
        # Run the test
        result = self.analyzer.robots_sitemap_analysis()
        
        # Verify results
        self.assertIsNotNone(result)
        self.assertIn('robots_txt', result)
        self.assertIn('robots_analysis', result)
        self.assertEqual(len(result['robots_analysis']['disallow_paths']), 2)
    
    @patch('requests.get')
    def test_subdomain_enumeration(self, mock_get):
        """Test subdomain enumeration functionality"""
        # Mock Certificate Transparency response
        ct_response = Mock()
        ct_response.status_code = 200
        ct_response.json.return_value = [
            {'name_value': 'www.example.com'},
            {'name_value': 'mail.example.com\napi.example.com'},
        ]
        
        mock_get.return_value = ct_response
        
        # Run the test
        with patch('socket.gethostbyname') as mock_dns:
            mock_dns.side_effect = ['*************', '*************']
            result = self.analyzer.subdomain_enumeration()
        
        # Verify results
        self.assertIsInstance(result, list)
        self.assertIn('subdomains', self.analyzer.results['osint'])
    
    def test_generate_recommendations(self):
        """Test recommendation generation"""
        # Set up test data with missing security headers
        self.analyzer.results['vulnerabilities'] = {
            'security_headers': {
                'Content-Security-Policy': 'Ausente',
                'Strict-Transport-Security': 'Ausente',
                'X-Frame-Options': 'Presente'
            }
        }
        
        # Run the test
        recommendations = self.analyzer.generate_recommendations()
        
        # Verify results
        self.assertIsInstance(recommendations, list)
        self.assertTrue(len(recommendations) > 0)
        
        # Check that recommendations are generated for missing headers
        csp_rec = any('Content-Security-Policy' in rec['issue'] 
                     for rec in recommendations)
        self.assertTrue(csp_rec)
    
    def test_risk_level_calculation(self):
        """Test risk level calculation logic"""
        # Test high risk scenario
        self.analyzer.results['vulnerabilities'] = {
            'security_headers': {
                'Content-Security-Policy': 'Ausente',
                'Strict-Transport-Security': 'Ausente',
                'X-Frame-Options': 'Ausente',
                'X-XSS-Protection': 'Ausente',
                'X-Content-Type-Options': 'Ausente',
                'Referrer-Policy': 'Ausente'
            }
        }
        
        risk_level = self.analyzer._calculate_risk_level()
        self.assertEqual(risk_level, 'Alto')
        
        # Test medium risk scenario
        self.analyzer.results['vulnerabilities']['security_headers'] = {
            'Content-Security-Policy': 'Ausente',
            'Strict-Transport-Security': 'Ausente',
            'X-Frame-Options': 'Presente',
            'X-XSS-Protection': 'Presente'
        }
        
        risk_level = self.analyzer._calculate_risk_level()
        self.assertEqual(risk_level, 'Bajo')
    
    def test_report_generation(self):
        """Test report generation functionality"""
        # Set up test data
        self.analyzer.results['recommendations'] = [
            {
                'category': 'Test',
                'issue': 'Test issue',
                'recommendation': 'Test recommendation',
                'priority': 'Alta'
            }
        ]
        
        # Run the test
        with tempfile.TemporaryDirectory() as temp_dir:
            original_cwd = os.getcwd()
            os.chdir(temp_dir)
            
            try:
                report = self.analyzer.generate_report()
                
                # Verify report structure
                self.assertIn('executive_summary', report)
                self.assertIn('detailed_findings', report)
                self.assertIn('recommendations', report)
                
                # Verify file was created
                self.assertTrue(hasattr(self.analyzer, 'report_filename'))
                self.assertTrue(os.path.exists(self.analyzer.report_filename))
                
                # Verify JSON content
                with open(self.analyzer.report_filename, 'r') as f:
                    saved_report = json.load(f)
                    self.assertEqual(saved_report['executive_summary']['domain'], 
                                   self.test_domain)
            
            finally:
                os.chdir(original_cwd)


class TestConfig(unittest.TestCase):
    """Test cases for configuration management"""
    
    def test_config_validation(self):
        """Test configuration validation"""
        # This should not raise an exception
        Config.validate_config()
    
    def test_proxy_configuration(self):
        """Test proxy configuration"""
        proxies = Config.get_proxies()
        # Should return None or a dict
        self.assertTrue(proxies is None or isinstance(proxies, dict))
    
    def test_headers_configuration(self):
        """Test HTTP headers configuration"""
        headers = Config.get_headers()
        self.assertIsInstance(headers, dict)
        self.assertIn('User-Agent', headers)


if __name__ == '__main__':
    # Run the tests
    unittest.main(verbosity=2)
