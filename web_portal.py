#!/usr/bin/env python3
"""
Portal Web OSINT - Versión Simplificada y Estable
================================================

Interfaz web simplificada para el framework OSINT
con mejor manejo de recursos y estabilidad mejorada.
"""

import os
import sys
import json
import glob
import datetime
from flask import Flask, render_template, request, jsonify, redirect, url_for, send_file
from flask_socketio import SocketIO, emit
import subprocess
import threading
import time
import signal

# Configurar para usar el framework OSINT
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

app = Flask(__name__)
app.config['SECRET_KEY'] = 'osint-security-key-2024'

# SocketIO con configuración simplificada
socketio = SocketIO(app, 
                   cors_allowed_origins="*",
                   async_mode='threading',
                   ping_timeout=30,
                   ping_interval=10)

# Variables globales simplificadas
current_analysis = None
analysis_lock = threading.Lock()

def cleanup_process():
    """Limpiar procesos al cerrar"""
    global current_analysis
    if current_analysis and current_analysis.poll() is None:
        try:
            current_analysis.terminate()
            current_analysis.wait(timeout=5)
        except:
            try:
                current_analysis.kill()
            except:
                pass

def signal_handler(sig, frame):
    """Manejo de señales para limpieza"""
    cleanup_process()
    sys.exit(0)

signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

def get_reports():
    """Obtener lista de reportes disponibles"""
    reports = []
    json_files = glob.glob("osint_report_*.json")
    
    for json_file in json_files:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Determinar si tiene archivo HTML
            html_file = json_file.replace('.json', '.html')
            has_html = os.path.exists(html_file)
            
            # Extraer información del reporte
            if 'executive_summary' in data:
                summary = data['executive_summary']
                domain = summary.get('domain', 'N/A')
                risk_level = summary.get('risk_level', 'N/A')
                findings = summary.get('total_findings', 0)
            else:
                # Formato alternativo
                domain = data.get('domain', 'N/A')
                risk_level = data.get('risk_assessment', {}).get('level', 'N/A')
                findings = len(data.get('recommendations', []))
            
            # Información del archivo
            stat = os.stat(json_file)
            date = datetime.datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M')
            
            reports.append({
                'filename': json_file,
                'domain': domain,
                'risk_level': risk_level,
                'findings': findings,
                'date': date,
                'has_html': has_html,
                'size': stat.st_size
            })
        except Exception as e:
            print(f"Error procesando {json_file}: {e}")
            continue
    
    # Ordenar por fecha (más recientes primero)
    reports.sort(key=lambda x: x['date'], reverse=True)
    return reports

@app.route('/')
def index():
    """Página principal del dashboard"""
    try:
        reports = get_reports()
        stats = {
            'total_reports': len(reports),
            'high_risk': len([r for r in reports if r['risk_level'] == 'Alto']),
            'medium_risk': len([r for r in reports if r['risk_level'] == 'Medio']),
            'low_risk': len([r for r in reports if r['risk_level'] == 'Bajo']),
            'latest_analysis': reports[0] if reports else None
        }
        return render_template('index.html', reports=reports, stats=stats)
    except Exception as e:
        print(f"Error en index: {e}")
        return render_template('index.html', reports=[], stats={})

@app.route('/analyze')
def analyze_page():
    """Página de configuración de análisis"""
    return render_template('analyze.html')

@app.route('/reports')
def reports_page():
    """Página de gestión de reportes"""
    try:
        reports = get_reports()
        return render_template('reports.html', reports=reports)
    except Exception as e:
        print(f"Error en reports: {e}")
        return render_template('reports.html', reports=[])

@app.route('/visualize/<filename>')
def visualize_report(filename):
    """Visualización de reportes"""
    try:
        # Verificar que el archivo existe
        if not os.path.exists(filename):
            return redirect(url_for('reports_page'))
        
        # Cargar datos del reporte
        with open(filename, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        # Preparar datos para gráficos
        charts = {
            'security_headers': {},
            'risk_level': {}
        }
        
        # Gráfico de cabeceras de seguridad
        if report_data.get('detailed_findings', {}).get('vulnerabilities', {}).get('security_headers'):
            headers = report_data['detailed_findings']['vulnerabilities']['security_headers']
            charts['security_headers'] = {
                'presente': len([h for h in headers.values() if h != 'Ausente']),
                'ausente': len([h for h in headers.values() if h == 'Ausente'])
            }
        
        return render_template('visualize.html', 
                             report=report_data,  # Cambiar 'report_data' por 'report'
                             charts=charts,
                             filename=filename)
    except Exception as e:
        print(f"Error visualizando {filename}: {e}")
        return redirect(url_for('reports_page'))

@app.route('/api/stats')
def api_stats():
    """API para estadísticas"""
    try:
        reports = get_reports()
        return jsonify({
            'domains_analyzed': len(set(r['domain'] for r in reports)),
            'high_risk': len([r for r in reports if r['risk_level'] == 'Alto']),
            'latest_analysis': reports[0] if reports else None,
            'total_reports': len(reports)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/report/<filename>')
def api_get_report(filename):
    """API para obtener datos de un reporte"""
    try:
        if not os.path.exists(filename):
            return jsonify({'error': 'Archivo no encontrado'}), 404
        
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        return jsonify(data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/start_analysis', methods=['POST'])
def start_analysis():
    """Iniciar análisis OSINT - versión simplificada"""
    global current_analysis
    
    try:
        data = request.get_json()
        domain = data.get('domain', '').strip()
        
        # Verificar si es análisis avanzado (compatibilidad con el frontend)
        is_advanced = data.get('advanced', False) or data.get('type') == 'advanced'
        
        if not domain:
            return jsonify({'success': False, 'error': 'Dominio requerido'}), 400
            
        # Verificar autorización
        if not data.get('authorized', False):
            return jsonify({'success': False, 'error': 'Autorización requerida'}), 400
        
        # Limpiar dominio
        domain = domain.replace('https://', '').replace('http://', '').replace('www.', '')
        
        with analysis_lock:
            if current_analysis and current_analysis.poll() is None:
                return jsonify({'success': False, 'error': 'Análisis en progreso'}), 400
            
            # Preparar comando
            if is_advanced:
                cmd = ['python', 'advanced_osint_framework.py', domain, '--authorized']
            else:
                cmd = ['python', 'osint_framework.py', domain, '--authorized']
            
            # Ejecutar análisis en proceso separado
            current_analysis = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
        
        # Monitorear progreso en hilo separado
        threading.Thread(target=monitor_analysis, args=(domain,), daemon=True).start()
        
        return jsonify({'success': True, 'status': 'started', 'domain': domain})
        
    except Exception as e:
        print(f"Error iniciando análisis: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

def monitor_analysis(domain):
    """Monitorear progreso del análisis de forma simplificada"""
    global current_analysis
    
    try:
        if not current_analysis:
            return
        
        # Emitir inicio
        socketio.emit('analysis_progress', {
            'message': 'Iniciando análisis...',
            'progress': 10,
            'domain': domain,
            'phase': 'Iniciando'
        })
        
        # Simular progreso mientras el proceso corre
        progress_steps = [
            (20, 'Reconocimiento DNS', 'Recolectando información básica...'),
            (40, 'Análisis SSL', 'Verificando certificados SSL...'),
            (60, 'Subdominios', 'Enumerando subdominios...'),
            (80, 'Tecnologías', 'Detectando tecnologías web...'),
            (90, 'Seguridad', 'Analizando cabeceras de seguridad...'),
        ]
        
        # Esperar a que termine el proceso con timeout
        start_time = time.time()
        step_index = 0
        
        while current_analysis.poll() is None:
            elapsed = time.time() - start_time
            
            # Emitir progreso cada 10 segundos
            if step_index < len(progress_steps) and elapsed > step_index * 10:
                progress, phase, message = progress_steps[step_index]
                socketio.emit('analysis_progress', {
                    'message': message,
                    'progress': progress,
                    'domain': domain,
                    'phase': phase
                })
                step_index += 1
            
            # Timeout después de 5 minutos
            if elapsed > 300:
                current_analysis.kill()
                break
                
            time.sleep(2)
        
        # Obtener resultado
        stdout, stderr = current_analysis.communicate()
        
        if current_analysis.returncode == 0:
            # Éxito - progreso final
            socketio.emit('analysis_progress', {
                'message': 'Generando informe final...',
                'progress': 95,
                'domain': domain,
                'phase': 'Informe'
            })
            
            time.sleep(2)
            
            socketio.emit('analysis_complete', {
                'status': 'success',
                'domain': domain,
                'message': 'Análisis completado exitosamente'
            })
        else:
            # Error
            error_msg = stderr.strip() if stderr else 'Error desconocido en el análisis'
            socketio.emit('analysis_error', {
                'domain': domain,
                'error': error_msg
            })
            
    except Exception as e:
        print(f"Error monitoreando análisis: {e}")
        socketio.emit('analysis_error', {
            'domain': domain,
            'error': f'Error interno: {str(e)}'
        })
    finally:
        current_analysis = None

@socketio.on('connect')
def handle_connect():
    """Manejo de conexión WebSocket"""
    print('Cliente conectado')

@socketio.on('disconnect')
def handle_disconnect():
    """Manejo de desconexión WebSocket"""
    print('Cliente desconectado')

if __name__ == '__main__':
    print("🌐 Iniciando Portal Web OSINT...")
    print("📊 Dashboard disponible en: http://localhost:8080")
    print("🔍 Análisis en tiempo real habilitado")
    
    try:
        socketio.run(app, 
                    host='0.0.0.0', 
                    port=8080, 
                    debug=True,
                    use_reloader=False)  # Evitar problemas con reloader
    except KeyboardInterrupt:
        print("\n🛑 Cerrando portal...")
        cleanup_process()
    except Exception as e:
        print(f"❌ Error en el portal: {e}")
        cleanup_process() 